/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2023-05-12 09:29:04
 */
import request from '@/utils/request';

// 查询菜单列表
export function listMenu(data) {
  return request({
    url: '/permissionInfo/s',
    method: 'post',
    params: { page: false, tree: true },
    data
  });
}

// 查询菜单详细
export function getMenu(menuId) {
  return request({
    url: '/system/menu/' + menuId,
    method: 'get'
  });
}

// 查询菜单下拉树结构
export function treeselect() {
  return request({
    url: '/system/menu/treeselect',
    method: 'get'
  });
}

// 根据角色ID查询菜单下拉树结构
export function roleMenuTreeselect(roleId) {
  return request({
    url: '/system/menu/roleMenuTreeselect/' + roleId,
    method: 'get'
  });
}

// 新增菜单
export function addMenu(data) {
  return request({
    url: '/permissionInfo',
    method: 'post',
    data
  });
}

// 修改菜单
export function updateMenu(data) {
  return request({
    url: `/permissionInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除菜单
export function delMenu(id) {
  return request({
    url: `/permissionInfo/${id}`,
    method: 'delete'
  });
}
// 删除菜单
export function delbatchMenu(data) {
  return request({
    url: `/permissionInfo/batchRemove`,
    method: 'delete',
    data
  });
}
