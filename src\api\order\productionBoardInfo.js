import request from '@/utils/request';
// 生产看板数据新建资源
export function add(data) {
  return request({ url: '/productionBoardInfo', method: 'post', data });
}
// 生产看板数据生产记录完成
export function finish(data) {
  return request({ url: '/productionBoardInfo/finish/' + data, method: 'get', params: data });
}
// 生产看板数据查询资源列表,支持分页
export function list(data) {
  return request({ url: '/productionBoardInfo/s', method: 'post', data });
}
// 生产看板数据资源是否存在
export function existing(data) {
  return request({ url: '/productionBoardInfo/s/existing', method: 'post', data });
}
// 生产看板数据批量删除资源,ids以，或 -分割
export function dels(data) {
  return request({ url: '/productionBoardInfo/' + data, method: 'delete' });
}
// 生产看板数据获取单个资源
export function detail(data) {
  return request({ url: '/productionBoardInfo/' + data, method: 'get', params: data });
}
// 生产看板数据修改资源
export function edit(data) {
  return request({ url: '/productionBoardInfo/' + data.id, method: 'put', data });
}
// 生产看板数据删除资源
export function del(data) {
  return request({ url: '/productionBoardInfo/' + data, method: 'delete' });
}
