<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2024-07-03 16:28:08
-->
<template>
  <commonModal ref="commonModalRef" dialog-title="解除异常" :footer-show="false" popup-width="800px">
    <ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :request-auto="false"
      :header-left-show="false"
      :header-search-show="false"
      :pagination="false"
    >
      <!-- 表格操作 -->
      <template #operation="scope">
        <el-button type="primary" link @click="saveData(scope.row)">选择</el-button>
      </template>
    </ProTable>
  </commonModal>
</template>

<script setup>
import { getHandlePerson, finishCall } from '@/api/home/<USER>';
import { nextTick } from 'vue';
const emit = defineEmits(['on-success']);
const commonModalRef = ref();
const columns = [
  { prop: 'receiver', label: '解除人员' },
  { prop: 'operation', label: '操作', fixed: 'right', width: 120 }
];
const proTable = ref();
function refreshTable() {
  proTable.value.getTableList();
}
const currentYichang = ref({});
// 初始化弹窗
function openModal(val) {
  commonModalRef.value.visible = true;
  currentYichang.value = val;
  nextTick(() => {
    refreshTable();
  });
}
// 加载数据
const getTableList = params => {
  return getHandlePerson(currentYichang.value.causeId);
};
// 保存数据
function saveData(row) {
  finishCall({ id: row.id, ...currentYichang.value }).then(res => {
    window.$modal.msgSuccess('操作成功');
    emit('on-success');
    commonModalRef.value.visible = false;
  });
}

defineExpose({
  openModal
});
</script>
