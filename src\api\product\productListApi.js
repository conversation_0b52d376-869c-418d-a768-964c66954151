/*
 * @Author: 方志良 
 * @Date: 2025-04-24 16:52:56
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-25 09:50:12
 * @FilePath: \yihe-front-end\src\api\product\productListApi.js
 * 
 */
import request from '@/utils/request';

// 获取产品
export const getPageApi = (data, params = {}) =>
  request({
    url: `/craftProductInfo/s`,
    method: 'post',
    data,
    params
  });

// 新增产品
export const addPageApi = data =>
  request({
    url: `/craftProductInfo`,
    method: 'post',
    data
  });

// 修改产品
export const editPageApi = data =>
  request({
    url: `/craftProductInfo/${data.id}`,
    method: 'put',
    data
  });

// 删除产品
export const delPageApi = data =>
  request({
    url: `/craftProductInfo/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });



// 获取产品工序
export const getProcessPageApi = (data, params = {}) =>
  request({
    url: `/craftProductProcess/s`,
    method: 'post',
    data,
    params
  });

// 新增产品工序
export const addProcessPageApi = data =>
  request({
    url: `/craftProductProcess`,
    method: 'post',
    data
  });

// 修改产品工序
export const editProcessPageApi = data =>
  request({
    url: `/craftProductProcess/${data.id}`,
    method: 'put',
    data
  });

// 删除产品工序
export const delProcessPageApi = data =>
  request({
    url: `/craftProductProcess/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });



// 获取产品SOP
export const getSOPPageApi = (data, params = {}) =>
  request({
    url: `/craftProductProcessSop/s`,
    method: 'post',
    data,
    params
  });

// 新增产品SOP
export const addSOPPageApi = data =>
  request({
    url: `/craftProductProcessSop`,
    method: 'post',
    data
  });

// 修改产品SOP
export const editSOPPageApi = data =>
  request({
    url: `/craftProductProcessSop/${data.id}`,
    method: 'put',
    data
  });

// 删除产品SOP
export const delSOPPageApi = data =>
  request({
    url: `/craftProductProcessSop/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });

// 上移产品SOP
export const upSOPPageApi = data =>
  request({
    url: `/productSop/up/${data.id}`,
    method: 'post',
    data
  });

// 下移产品SOP
export const downSOPPageApi = data =>
  request({
    url: `/productSop/down/${data.id}`,
    method: 'post',
    data
  });


// 根据工位获取产品SOP
export const getStationSOPPageApi = (params = {}) =>
  request({
    url: `/openApi/getSopList`,
    method: 'post',
    params
  });

