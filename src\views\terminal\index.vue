<!--
 * @Author: babyyage
 * @Date: 2024-07-02 14:49:52
 * @LastEditTime: 2025-06-20 10:33:16
-->
<template>
  <div
    class="home"
    :style="`position: ${
      !screenState ? 'fixed' : 'relative'
    };top:0;right:0;left:0;bottom:0`"
  >
    <div class="elcard">
      <div class="home-header">
        <div class="home-header-item">
          <div>
            <span class="title">工作作业:</span>
            <el-button type="primary" @click="tochooseStation" style="margin-left: 10px">
              工位配置
            </el-button>
            <span class="title" style="margin: 0 44px; font-weight: 500">
              产线工位：{{ `${lineName}-${stationName}` }}
            </span>

            <!-- <el-button
              type="primary"
              style="margin: 0 10px; height: 32px"
              @click="huanXingBtn"
            >
              选择产品
            </el-button>
            <span class="title" style="margin: 0 15px; font-weight: 500">
              产品编码:{{ productCode }}
            </span> -->
          </div>
        </div>
        <div class="home-header-item">
          <div style="display: flex; align-items: center">
            <div class="title">自动模式:</div>
            <el-switch
              v-model="isAutoFlag"
              inline-prompt
              active-text="是"
              inactive-text="否"
              @change="isAutoFlagFn"
            />

            <div class="title" style="margin-left: 20px">生产模式:</div>
            <el-switch
              v-model="isProductFlag"
              inline-prompt
              active-text="是"
              inactive-text="否"
              @change="changeProductFn"
            />

            <el-button
              :type="isProductFlag ? 'info' : 'primary'"
              :disabled="isProductFlag"
              style="margin: 0 10px; height: 32px"
              @click="sopFn('手动模式')"
            >
              SOP查询
            </el-button>
            <el-button style="margin-left: 10px; height: 32px" @click="handlerScreenFull">
              {{ screenState ? "全屏" : "取消全屏" }}
            </el-button>

            <el-button style="margin-left: 10px" type="danger" @click="logout">
              退出登录
            </el-button>
          </div>
        </div>
      </div>
      <div class="home-main">
        <div class="home-main-left">
          <div class="top-sop">
            <SOP :sop-arr="sopArr" :sop-switch="false" ref="SOPRef" />
          </div>
          <div class="announcement">
            <div class="announcement-left">通知公告</div>
            <div class="announcement-right">
              <div class="content">
                <div
                  class="scroll-text"
                  :class="{ 'scroll-animation': true }"
                  ref="scrollTextRef"
                >
                  {{ noticeContent }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="home-main-right">
          <Andon ref="AndonRef" />
        </div>
      </div>
    </div>

    <StationModal ref="StationModalRef" @on-success="StationModalFn"></StationModal>
    <AllProduct ref="AllProductRef" @on-success="AllProductFn"></AllProduct>
    <HuanXing ref="HuanXingRef" @on-success="HuanXingFn" />
  </div>
</template>

<script setup>
import SOP from "./sop/index.vue";
import screenfull from "screenfull";
import StationModal from "./modal/StationModal.vue";
import AllProduct from "./product/AllProduct.vue";
const HuanXing = defineAsyncComponent(() => import("./modal/HuanXing.vue"));
import * as productList from "@/api/product/productListApi.js";
import Andon from "./andon/index.vue";

//配置
const stationName = ref(localStorage.getItem("terminal.stationName") || "");
const lineName = ref(localStorage.getItem("terminal.lineName") || "");
let StationModalRef = ref(null);
function tochooseStation() {
  StationModalRef.value.openModal();
}
let AndonRef = ref(null);
const StationModalFn = (line, station) => {
  stationName.value = line;
  lineName.value = station;
  AndonRef.value.openModal();
};

let isProductFlag = ref(false);

onMounted(() => {
  if (localStorage.getItem("terminal.stationName")) {
    if (localStorage.getItem("terminal.sopModel") == "手动模式") {
      sopArr.value = localStorage.getItem("terminal.sopList")
        ? JSON.parse(localStorage.getItem("terminal.sopList"))
        : [];
    } else {
      sopArr.value = [];
    }
  }

  // 页面加载完成后检查滚动状态
  // nextTick(() => {
  //   setTimeout(() => {
  //     checkIfNeedScroll();
  //   }, 500);
  // });
});

const changeProductFn = (val) => {
  if (val) {
    sopFn("生产模式");
  } else {
    clearInterval(autoTimer.value);
  }
};

const screenState = ref(screenfull.isFullscreen || true);
function handlerScreenFull() {
  if (screenfull.isEnabled) {
    // 加个判断浏览器是否支持全屏
    screenfull.toggle(); // 切换全屏状态
  } else {
    this.$message.info("您的浏览器不支持全屏");
  }
  screenState.value = screenfull.isFullscreen;
}

//sop模式
const sopArr = ref(
  localStorage.getItem("terminal.sopList")
    ? JSON.parse(localStorage.getItem("terminal.sopList"))
    : []
);
const getSOPListFn = async () => {
  if (!localStorage.getItem("terminal.stationId")) {
    isProductFlag.value = false;
    return window.$modal.msgError("请点击工位配置选择产线工位");
  }
  try {
    let res = await productList.getStationSOPPageApi({
      stationId: localStorage.getItem("terminal.stationId"),
    });
    if (JSON.stringify(res.data) != JSON.stringify(sopArr.value)) {
      sopArr.value = res.data;
    }
  } catch (error) {
    console.log(error, "err");
    isProductFlag.value = false;
  }
};
let sopModel = ref(localStorage.getItem("terminal.sopModel") || "生产模式");
let AllProductRef = ref(null);

let autoTimer = ref(null);
const sopFn = (val) => {
  localStorage.setItem("terminal.sopModel", val);
  sopModel.value = val;
  if (sopModel.value == "生产模式") {
    getSOPListFn();
    autoTimer.value = setInterval(() => {
      getSOPListFn();
    }, 10000);
  } else {
    sopArr.value = [];
    AllProductRef.value.openModal();
  }
};

//手动SOP
const AllProductFn = (arr) => {
  localStorage.setItem("terminal.sopList", JSON.stringify(arr));
  sopArr.value = arr;
};

onBeforeUnmount(() => {});

//换型
const HuanXingRef = ref(null);
const huanXingBtn = () => {
  if (!localStorage.getItem("terminal.stationCode"))
    return window.$modal.msgError("请点击工位配置选择产线工位");
  HuanXingRef.value.openModal();
};

const productCode = ref(localStorage.getItem("terminal.productCode") || "");
const HuanXingFn = (obj, arr) => {
  productCode.value = obj.productCode;
  console.log(obj, "返回数据");
  localStorage.setItem("terminal.productCode", obj.productCode);
  localStorage.setItem("terminal.productId", obj.id);
  AllProductFn(arr);
};

//获取公告
import { listnotice } from "@/api/home/<USER>";
let noticeContent = ref("");
// let shouldScroll = ref(false);
let scrollTextRef = ref(null);

const listnoticeFn = async () => {
  let res = await listnotice({ pageIndex: 0, pageSize: 1 });
  if (res.data && res.data.length > 0) {
    noticeContent.value = res.data[0].content;
  } else {
    noticeContent.value = "暂无公告";
  }

  // 检查内容是否需要滚动
  // nextTick(() => {
  //   checkIfNeedScroll();
  // });
};

// const checkIfNeedScroll = () => {
//   // 使用setTimeout确保DOM完全渲染
//   setTimeout(() => {
//     const contentElement = document.querySelector(".announcement-right .content");
//     const textElement = scrollTextRef.value;

//     if (contentElement && textElement) {
//       console.log(textElement.scrollWidth, contentElement.clientWidth, "滚动");
//       // 如果文本宽度超过容器宽度，则启用滚动
//       const needScroll = textElement.scrollWidth > contentElement.clientWidth;
//       shouldScroll.value = needScroll;
//       console.log("shouldScroll:", shouldScroll.value);
//       console.log("CSS类应用状态:", textElement.classList.contains("scroll-animation"));
//     }
//   }, 1000);
// };

listnoticeFn();

//退出登录
import useUserStore from "@/store/modules/user";
const userStore = useUserStore();
import { ElMessageBox } from "element-plus";
const router = useRouter();
function logout() {
  ElMessageBox.confirm("确定注销并退出系统吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      userStore.logOut().then((res) => {
        console.log(res, "退出");
        router.replace("/login");
      });
    })
    .catch(() => {});
}

//自动模式
let SOPRef = ref(null);
let isAutoFlag = ref(false);
const isAutoFlagFn = (val) => {
  SOPRef.value.timeChange(val);
};
</script>

<style lang="scss" scoped>
.home {
  .elcard {
    width: 100%;
    height: 100%;
    background-color: #0852e1;
    padding: 20px;
  }
  .home-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 60px;
    overflow-x: scroll;

    &-item {
      margin-top: 10px;
    }

    .title {
      font-size: 16px;
      font-weight: 600;
      color: #ffffff;
    }
  }

  .home-main {
    width: 100%;
    height: calc(100vh - 100px);
    display: flex;
    justify-content: space-between;

    &-left {
      width: 75%;
      margin-right: 20px;

      .top-sop {
        width: 100%;
        height: calc(100% - 60px);
      }
      .announcement {
        margin-top: 10px;
        height: 60px;
        background-color: #dbebff;
        border-radius: 4px;
        display: flex;
        max-width: 100%; /* 防止容器被撑大 */
        overflow: hidden; /* 隐藏溢出的部分 */

        &-left {
          width: 150px;
          height: 100%;
          line-height: 60px;
          color: #ffffff;
          text-align: center;
          font-size: 25px;
          background: linear-gradient(to bottom, #3ab5f4, #1255fd);
          border-radius: 4px;
        }
        &-right {
          flex: 1;
          height: 100%;
          padding-left: 30px;
          line-height: 60px;
          font-size: 25px;
          color: #000000;
          width: 0; /* 关键：给flex容器设置width: 0 */
          .content {
            width: 100%;
            height: 100%;
            overflow: hidden; /* 隐藏溢出内容 */
            position: relative;

            .scroll-text {
              white-space: nowrap; /* 禁止换行 */
              display: inline-block;
              width: max-content; /* 确保文本有足够宽度 */

              &.scroll-animation {
                animation: scrollLeft 40s linear infinite;
              }
            }
          }
        }
      }
    }

    &-right {
      width: 25%;
      height: 100%;

      .order-btn {
        width: 100%;
        display: flex;
        justify-content: center;
      }
    }
  }
}

.soptitle {
  font-size: 18px;
  margin-right: 10px;
  font-weight: 700;
}

/* 滚动动画 - 使用全局样式确保不被scoped影响 */
</style>

<style>
/* 全局滚动动画样式 */
@keyframes scrollLeft {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.scroll-animation {
  animation: scrollLeft 40s linear infinite !important;
}
</style>
