<!--
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-29 15:12:44
-->
<template>
  <section class="app-main" :style="`border-top-left-radius: ${!isFirstView() ? '10px' : '0px'};`">
    <router-view v-slot="{ Component, route }" :key="key">
      <transition name="fade-transform" mode="out-in">
        <keep-alive :include="tagsViewStore.cachedViews">
          <component :is="Component" v-if="!route.meta.link" :key="route.path" />
        </keep-alive>
      </transition>
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup>
import iframeToggle from './IframeToggle/index';
import useTagsViewStore from '@/store/modules/tagsView';
const tagsViewStore = useTagsViewStore();
const visitedViews = computed(() => useTagsViewStore().visitedViews);
const route1 = useRoute();
const key = computed(() => {
  return route1.path + Math.random();
});
function isFirstView() {
  try {
    return route1.path === visitedViews.value[0].fullPath;
  } catch (err) {
    return false;
  }
}
</script>

<style lang="scss" scoped>
.app-main {
  position: relative;

  // height: 98%;
  width: auto;

  /* 50= navbar  50  */
  height: calc(100% - 80px);
  padding: 24px;
  overflow: auto;
  background-color: #ffffff;

  // border-radius: 10px;
  border-top-right-radius: 10px;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
}

.fixed-header + .app-main {
  padding-top: 50px;
}

.hasTagsView {
  .app-main {
    /* 84 = navbar + tags-view = 50 + 34 */
    // min-height: calc(100vh - 84px);
    height: 100%;
  }

  .fixed-header + .app-main {
    padding-top: 84px;
  }
}
</style>
