<!--
 * @Author: babyyage
 * @Date: 2024-01-25 15:38:15
 * @LastEditTime: 2024-09-05 15:36:13
-->
<template>
  <div class="pageHeader">
    <div class="pageHeader-logo">
      <img v-if="leftLogoData.selectPic" :src="leftLogoData.topLeftUrl" alt="" />
      <div v-if="!leftLogoData.selectPic" style="font-size: 24px; font-weight: 700; color: #000000">
        {{ leftLogoData.topLeftName }}
      </div>
    </div>
    <div class="pageHeader-menus">
      <!-- <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
        <el-tab-pane
          v-for="(item, index) in sidebarRouters"
          :key="index"
          :label="getMenuTitle(item)"
          :name="item.path"
        ></el-tab-pane>
      </el-tabs> -->
    </div>
    <div class="pageHeader-tools">
      <el-badge v-if="isShowNotification" :is-dot="!!noticeCount" class="item" @click="toNotice">
        <svg-icon icon-class="notification" style="width: 20px; height: 22px; cursor: pointer" />
      </el-badge>
      <div class="avatar-container" style="margin-right: 0; margin-left: 0">
        <!-- <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
          <svg-icon icon-class="locale" style="width: 20px; height: 22px; cursor: pointer" />
          <template #dropdown>
            <el-dropdown-menu>
              <a>
                <el-dropdown-item @click="changeLocale('zh-cn')">中文</el-dropdown-item>
              </a>
              <a>
                <el-dropdown-item @click="changeLocale('en-us')">English</el-dropdown-item>
              </a>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
      </div>
      <div class="avatar-container" style="margin-left: 0">
        <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
          <div class="avatar-wrapper">
            <img :src="logo" class="user-avatar" />
          </div>
          <template #dropdown>
            <el-dropdown-menu>
              <a>
                <el-dropdown-item @click="toResetPsd">{{ $t('login.editpassword') }}</el-dropdown-item>
              </a>
              <a>
                <el-dropdown-item @click="logout">{{ $t('login.logout') }}</el-dropdown-item>
              </a>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    <ResetPassword ref="resetPassword" />
    <NoticeModal ref="noticeModal" />
  </div>
</template>

<script setup>
import { defineAsyncComponent } from 'vue';
import { ElMessageBox } from 'element-plus';
import useUserStore from '@/store/modules/user';
import logo from '@/assets/images/page-avatar.png';
import { requireImg } from '@/utils/file';
import { getAppearanceInfo2, getUnreadList } from '@/api/system/user';
import defaultSettings from '@/settings';
import { getToken } from '@/utils/auth';
import { EventSourcePolyfill } from 'event-source-polyfill';
import Cookies from 'js-cookie';
const { isShowNotification } = defaultSettings;
const ResetPassword = defineAsyncComponent(() => import('../modal/ResetPassword'));
const NoticeModal = defineAsyncComponent(() => import('./NoticeModal'));
const userStore = useUserStore();
const router = useRouter();

const noticeCount = ref(0);
onMounted(() => {
  if (isShowNotification) {
    getUnreadList({ id: Cookies.get('userId') }).then(res => {
      noticeCount.value = res.data || 0;
      const source = createContent(
        `${import.meta.env.VITE_APP_BASE_API}/sse/createConnect?userId=${Cookies.get('userId')}`,
        getToken()
      );
      source.onmessage = function (event) {
        noticeCount.value = event.data || 0;
      };
      source.onopen = function (event) {};
    });
  }
});

function createContent(url, token) {
  return new EventSourcePolyfill(url, {
    headers: {
      // Authorization: token,
      // 'Content-Type': 'text/event-stream'
    }
  });
}
const leftLogoData = ref({
  selectPic: true,
  topLeftName: '',
  topLeftUrl: ''
});
function getImgs() {
  getAppearanceInfo2().then(res => {
    const data = res.data[0];
    leftLogoData.value.selectPic = data.selectPic;
    leftLogoData.value.topLeftName = data.topLeftName || '';
    leftLogoData.value.topLeftUrl = data.topLeftUrl
      ? import.meta.env.VITE_APP_BASE_API + data.topLeftUrl
      : requireImg('login-logo.png');
  });
}
getImgs();
// console.log(sidebarRouters.value);

function handleCommand(command) {
  switch (command) {
    case 'setLayout':
      setLayout();
      break;
    case 'logout':
      logout();
      break;
    default:
      break;
  }
}
function logout() {
  ElMessageBox.confirm(window.$t('login.logout2'), window.$t('general.warningInfo'), {
    confirmButtonText: window.$t('general.btnconfirm'),
    cancelButtonText: window.$t('general.btncancel'),
    type: 'warning'
  })
    .then(() => {
      userStore.logOut().then(() => {
        router.replace('/login');
      });
    })
    .catch(() => {});
}

const resetPassword = ref();
function toResetPsd() {
  resetPassword.value.openModal({});
}
const noticeModal = ref();
function toNotice() {
  noticeModal.value.openModal({});
}

const emits = defineEmits(['set-layout']);
function setLayout() {
  emits('set-layout');
}
</script>

<style lang="scss">
.pageHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 66px;
  background-color: #ffffff;

  .svg-icon:focus {
    outline: none;
  }

  &-logo {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 240px;
    height: 50px;
    padding-left: 10px;

    img {
      max-width: 170px;
      max-height: 50px;
    }
  }

  &-menus {
    width: 75%;

    .el-tabs__nav-wrap::after {
      display: none;
      height: 0;
    }

    .el-tabs__item {
      width: 135px;
      padding: 0;
      font-size: 16px;
      color: #555555;

      &:hover {
        color: var(--el-color-primary) !important;
      }
    }

    .el-tabs__item.is-active {
      color: var(--el-color-primary) !important;
    }

    .el-tabs__header {
      margin: 0 !important;
    }

    .el-tabs {
      --el-tabs-header-height: 66px;
    }

    .el-tabs__nav-next,
    .el-tabs__nav-prev {
      line-height: 66px;
    }
  }

  &-tools {
    display: flex;
    align-items: center;
    justify-content: space-between;
    float: right;
    height: 100%;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      height: 100%;
      padding: 0 8px;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgb(0 0 0 / 2.5%);
        }
      }
    }

    .avatar-container {
      margin-right: 10px;
      margin-left: 20px;

      .avatar-wrapper {
        position: relative;
        margin-top: 5px;

        .user-avatar {
          width: 40px;
          height: 40px;
          cursor: pointer;
          border-radius: 10px;
        }

        i {
          position: absolute;
          top: 25px;
          right: -20px;
          font-size: 12px;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
