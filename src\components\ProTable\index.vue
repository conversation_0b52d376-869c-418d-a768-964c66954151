<template>
  <!-- 查询表单 card -->
  <commonTableHeader
    v-show="isShowSearch"
    :search="search"
    :reset="reset"
    :search-param="searchParam"
    :toadd="toadd"
    :todelete="todelete"
    :clear-search-form="clearSearchForm"
    :selected-list-ids="selectedListIds"
    :header-left-show="headerLeftShow"
    :header-search-show="headerSearchShow"
    :header-left-add-text="headerLeftAddTextTwo"
    :header-left-del-text="headerLeftDelTextTwo"
    :placeholder-text="placeholderTextTwo"
    :keyword-input-width="keywordInputWidth"
    :add-access="addAccess"
    :del-access="delAccess"
  >
    <template #btnList>
      <slot name="btnList" :selected-list-ids="selectedListIds"></slot>
    </template>
    <template v-if="$slots.headerLeft" #headerLeft>
      <slot name="headerLeft"></slot>
    </template>
    <template v-if="$slots.headerSearch" #headerSearch>
      <slot name="headerSearch"></slot>
    </template>
    <template v-if="$slots.headerSearchFormItem" #headerSearchFormItem>
      <SearchForm :search="search" :reset="reset" :search-param="searchParam" :columns="searchColumns" :search-col="searchCol" />
      <slot name="headerSearchFormItem"></slot>
    </template>
  </commonTableHeader>

  <!-- 表格内容 card -->
  <div class="table-main">
    <!-- 表格头部 操作按钮 -->
    <div class="table-header">
      <div class="header-button-lf">
        <slot name="tableHeader" :selected-list-ids="selectedListIds" :selected-list="selectedList" :is-selected="isSelected" />
      </div>
      <div v-if="toolButton" class="header-button-ri">
        <slot name="toolButton">
          <!-- <el-button :icon="Refresh" circle @click="getTableList" /> -->
          <!-- <el-button :icon="Printer" circle v-if="columns.length" @click="handlePrint" /> -->
          <!-- <el-button v-if="columns.length" :icon="Operation" circle @click="openColSetting" /> -->
          <!-- <el-button v-if="searchColumns.length" :icon="Search" circle @click="isShowSearch = !isShowSearch" /> -->
        </slot>
      </div>
    </div>
    <!-- 表格主体 -->
    <el-table
      ref="tableRef"
      :data="tableData"
      :border="border"
      :row-key="rowKey"
      v-bind="$attrs"
      @selection-change="selectionChange"
    >
      <!-- 默认插槽 -->
      <slot></slot>
      <template v-for="(item, itemKey) in tableColumns" :key="itemKey">
        <!-- selection || index -->
        <el-table-column
          v-if="item.type == 'selection' || item.type == 'index'"
          v-bind="item"
          :align="item.align || 'center'"
          :reserve-selection="item.type == 'selection'"
        >
        </el-table-column>
        <!-- expand 支持 jsx 语法 && 作用域插槽 (jsx > slot) -->
        <el-table-column v-if="item.type == 'expand'" v-slot="scope" v-bind="item" :align="item.align || 'center'">
          <component :is="item.render" v-bind="scope" v-if="item.render"> </component>
          <slot v-else :name="item.type" v-bind="scope"></slot>
        </el-table-column>
        <!-- other 循环递归 -->
        <TableColumn v-if="!item.type && item.prop && item.isShow" :column="item">
          <template v-for="slot in Object.keys($slots)" #[slot]="scope">
            <slot :name="slot" v-bind="scope"></slot>
          </template>
        </TableColumn>
      </template>
      <!-- 插入表格最后一行之后的插槽 -->
      <template #append>
        <slot name="append"> </slot>
      </template>
      <!-- 表格无数据情况 -->
      <template #empty>
        <slot name="empty">
          <img src="@/assets/images/notData.png" alt="notData" style="margin-top: 10px" />
          <div style="line-height: 30px">暂无数据</div>
        </slot>
        <div class="table-empty"></div>
      </template>
    </el-table>
    <!-- 分页组件 -->
    <slot name="pagination">
      <Pagination
        v-if="pagination"
        :pageable="pageable"
        :handle-size-change="handleSizeChange"
        :handle-current-change="handleCurrentChange"
      />
    </slot>
  </div>
  <!-- 列设置 -->
  <ColSetting v-if="toolButton" ref="colRef" v-model:col-setting="colSetting" />
</template>

<script setup name="ProTable">
import { ref, watch, provide, onMounted, computed } from 'vue';
import { useTable } from './hooks/useTable';
import { useSelection } from './hooks/useSelection';
import { ElTable } from 'element-plus';
import { handleProp } from '@/utils/index';
import SearchForm from '@/components/SearchForm/index2.vue';
import Pagination from './components/Pagination.vue';
import ColSetting from './components/ColSetting.vue';
import TableColumn from './components/TableColumn.vue';
const props = defineProps({
  requestApi: {
    type: Function,
    default: () => {}
  },
  toadd: {
    type: Function,
    default: () => {}
  },
  todelete: {
    type: Function,
    default: () => {}
  },
  clearSearchForm: {
    type: Function,
    default: () => {}
  },
  requestAuto: {
    type: Boolean,
    default: true
  },
  handleData: {
    type: Boolean,
    default: false
  },
  headerLeftShow: {
    type: Boolean,
    default: true
  },
  headerSearchShow: {
    type: Boolean,
    default: true
  },
  handleDataFunc: {
    type: Function,
    default: () => {}
  },
  pagination: {
    type: Boolean,
    default: true
  },
  border: {
    type: Boolean,
    default: true
  },
  toolButton: {
    type: Boolean,
    default: false
  },
  isShowSearch: {
    type: Boolean,
    default: true
  },
  columns: {
    type: Array,
    default: () => []
  },
  initParam: {
    type: Object,
    default: () => {}
  },
  rowKey: {
    type: String,
    default: 'id'
  },
  headerLeftAddText: {
    type: String,
    default: ''
  },
  headerLeftDelText: {
    type: String,
    default: ''
  },
  placeholderText: {
    type: String,
    default: ''
  },
  searchCol: {
    type: Object,
    default: () => ({ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 })
  },
  keywordInputWidth: {
    type: Number,
    default: 190
  },
  addAccess: {
    type: String,
    default: '*:*:*:*'
  },
  delAccess: {
    type: String,
    default: '*:*:*:*'
  },
  queryAccess: {
    type: String,
    default: '*:*:*:*'
  }
});
const headerLeftAddTextTwo = computed(() => props.headerLeftAddText || window.$t('general.btnadd'));
const headerLeftDelTextTwo = computed(() => props.headerLeftDelText || window.$t('general.btndelete'));
const placeholderTextTwo = computed(() => props.placeholderText || window.$t('general.placeholderinput'));
// 是否显示搜索模块
// const isShowSearch = ref(true);

// 表格 DOM 元素
const tableRef = ref();

// 表格多选 Hooks
const { selectionChange, selectedList, selectedListIds, isSelected } = useSelection(props.rowKey);

// 表格操作 Hooks
const { tableData, pageable, searchParam, searchInitParam, getTableList, search, reset, handleSizeChange, handleCurrentChange } =
  useTable(
    props.requestApi,
    props.initParam,
    props.pagination,
    props.dataCallback,
    props.requestError,
    props.handleData,
    props.handleDataFunc,
    props.columns
  );
// 清空选中数据列表
const clearSelection = () => tableRef.value.clearSelection();

// 初始化请求
onMounted(() => props.requestAuto && getTableList());

// 监听页面 initParam 改化，重新获取表格数据
watch(() => props.initParam, getTableList, { deep: true });

// 监听表格数据的变化  如果相对数据进行特殊处理 可在这里操作
// watch(
//   () => tableData,
//   (newValue, oldValue) => {
//     console.log('值发生了变更', newValue, oldValue);
//     if (props.handleData && tableData.value.length) {
//       props.handleDataFunc(tableData.value);
//     }
//   },
//   { deep: true, immediate: true }
// );

// 接收 columns 并设置为响应式
const tableColumns = ref(props.columns);

// 定义 enumMap 存储 enum 值（避免异步请求无法格式化单元格内容 || 无法填充搜索下拉选择）
const enumMap = ref(new Map());
provide('enumMap', enumMap);
const setEnumMap = async col => {
  if (!col.enum) return;
  // 如果当前 enum 为后台数据需要请求数据，则调用该请求接口，并存储到 enumMap
  if (typeof col.enum !== 'function') return enumMap.value.set(col.prop, col.enum);
  const { data } = await col.enum();
  enumMap.value.set(col.prop, data);
};

// 扁平化 columns
const flatColumnsFunc = (columns, flatArr = []) => {
  columns.forEach(async col => {
    if (col._children?.length) flatArr.push(...flatColumnsFunc(col._children));
    flatArr.push(col);
    // 给每一项 column 添加 isShow && isFilterEnum 默认属性
    col.isShow = col.isShow === undefined ? true : col.isShow;
    col.isFilterEnum = col.isFilterEnum || true;

    // 设置 enumMap
    setEnumMap(col);
  });
  return flatArr.filter(item => !item._children?.length);
};

// flatColumns
const flatColumns = ref([]);
flatColumns.value = flatColumnsFunc(tableColumns.value);

// 过滤需要搜索的配置项   item.show
const searchColumns = flatColumns.value.filter(item => item.search?.el && item.show);

// 设置搜索表单排序默认值 && 设置搜索表单项的默认值
searchColumns.forEach((column, index) => {
  column.search.order = column.search?.order || index + 2;
  if (column.search?.defaultValue !== undefined && column.search?.defaultValue !== null) {
    searchInitParam.value[column.search.key || handleProp(column.prop)] = column.search?.defaultValue;
    searchParam.value[column.search.key || handleProp(column.prop)] = column.search?.defaultValue;
  }
});

// 排序搜索表单项
searchColumns.sort((a, b) => a.search.order - b.search.order);

// 列设置 ==> 过滤掉不需要设置的列
const colRef = ref();
const colSetting = tableColumns.value.filter(
  item => !['selection', 'index', 'expand'].includes(item.type) && item.prop !== 'operation' && item.isShow
);

// 暴露给父组件的参数和方法(外部需要什么，都可以从这里暴露出去)
defineExpose({
  element: tableRef,
  tableData,
  searchParam,
  pageable,
  getTableList,
  reset,
  clearSelection,
  enumMap,
  isSelected,
  selectedList,
  selectedListIds
});
</script>
