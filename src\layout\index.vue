<template>
  <div id="appWrapper" class="app-wrapper" :style="{ '--current-color': theme }">
    <div class="main-container">
      <el-scrollbar>
        <PageHeader />
        <div class="main-container-content">
          <div class="content-side">
            <Sidebar class="sidebar-container" />
          </div>
          <div class="content-main">
            <TagsView style="margin-top: 25px" />
            <AppMain />
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup name="IndexMain">
import { defineAsyncComponent, watchEffect } from 'vue';
import { useWindowSize } from '@vueuse/core';
import useAppStore from '@/store/modules/app';
import useSettingsStore from '@/store/modules/settings';
import { requireImg } from '@/utils/file'; // refer to Bootstrap's responsive design
import { getAppearanceInfo2 } from '@/api/system/user';
const AppMain = defineAsyncComponent(() => import('./components/AppMain.vue'));
const TagsView = defineAsyncComponent(() => import('./components/TagsView/index.vue'));
const PageHeader = defineAsyncComponent(() => import('./components/PageHeader/index.vue'));
const Sidebar = defineAsyncComponent(() => import('./components/Sidebar/index.vue'));
const settingsStore = useSettingsStore();
const theme = computed(() => settingsStore.theme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const { width } = useWindowSize();
const WIDTH = 992;
watchEffect(() => {
  if (device.value === 'mobile' && sidebar.value.opened) {
    useAppStore().closeSideBar({ withoutAnimation: false });
  }
  if (width.value - 1 < WIDTH) {
    useAppStore().toggleDevice('mobile');
    useAppStore().closeSideBar({ withoutAnimation: true });
  } else {
    useAppStore().toggleDevice('desktop');
  }
});
function getImgs() {
  getAppearanceInfo2().then(res => {
    const data = res.data[0];
    const bgImg = data.sysBackgroundUrl ? import.meta.env.VITE_APP_BASE_API + data.sysBackgroundUrl : requireImg('app-bg.png');
    document.getElementById('appWrapper').style.backgroundImage = `url('${bgImg}')`;
  });
}
getImgs();
</script>

<style lang="scss" scoped>
@import '@/assets/styles/mixin.scss';
@import '@/assets/styles/variables.module.scss';

.app-wrapper {
  @include clearfix;

  position: relative;
  width: 100%;
  height: 100%;

  // background-image: url('../assets/images/app-bg.png');
  background-size: cover;

  .el-scrollbar {
    height: 100%;
  }

  :deep(.el-scrollbar__bar).is-vertical {
    z-index: 10;
  }

  :deep(.el-scrollbar__wrap) {
    overflow-x: hidden;
  }

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  position: absolute;
  top: 0;
  z-index: 999;
  width: 100%;
  height: 100%;
  background: #000000;
  opacity: 0.3;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}
</style>
