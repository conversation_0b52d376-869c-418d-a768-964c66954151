<template>
  <commonModal ref="commonModalRef" width="80%" :dialog-title="props.title" :footer-show="props.single" @submit-data="saveData">
    <el-row>
      <slot name="info"></slot>
    </el-row>
    <el-row>
      <slot name="btnList"></slot>
    </el-row>

    <div class="table-box">
      <ProTable
        ref="proTable"
        :columns="props.columns"
        :request-api="getTableList"
        :request-auto="false"
        :handleData="handleData"
        :handle-data-func="handleDataFunc"
      >
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button v-if="!props.selectAll" type="primary" link @click="handleSelectRow(scope.row)">{{
            props.optionText
          }}</el-button>
          <slot name="operation" :row="scope.row"></slot>
        </template>
        <template v-for="item in slotList" #[item.prop]="scope">
          <slot :name="item.prop" :row="scope.row"></slot>
        </template>
      </ProTable>
    </div>
  </commonModal>
</template>

<script setup name="selfSelectModal">
// 单选
const { proxy } = getCurrentInstance();
const emit = defineEmits(['select']);
const props = defineProps({
  title: { default: '请选择', type: String },
  optionText: { default: '选择', type: String },
  columns: { default: () => [], type: Array },
  obj: { default: () => {}, type: Object },
  api: { default: function () {}, type: Function },
  single: { default: true, type: Boolean },
  selectAll: { default: false, type: Boolean }, // 提交所有数据
  calc: { default: false, type: Boolean }, // 是否对返回结果进行处理
  handleCalc: { default: function () {}, type: Function },
  handleData: { default: false, type: Boolean }, // 处理结果
  handleDataFunc: {
    type: Function,
    default: () => {}
  }
});
const proTable = ref();
const commonModalRef = ref();
const slotList = ref([]);
const para2 = ref({});
// 初始化弹窗
function openModal(obj) {
  slotList.value = props.columns.filter(item => item.slot);
  para2.value = obj;
  commonModalRef.value.visible = true;
  nextTick(() => {
    proTable.value.getTableList();
  });
}

function handleSelectRow(row) {
  commonModalRef.value.visible = false;
  emit('select', [row]);
}
const getTableList = async params => {
  proTable.value.clearSelection();
  const matcheres = [];
  for (const key of Object.keys(params)) {
    key != 'pageIndex' && key != 'pageSize' && matcheres.push({ name: key, type: 'B' });
  }
  try {
    let data = await props.api({ ...params, startStopStatus: 'ENABLE', ...props.obj, ...para2.value, matcheres });
    props.calc && (data = props.handleCalc(data));
    return data;
  } catch (error) {
    console.log(error);
  }
};
function refreshTable() {
  proTable.value.getTableList();
}
// 保存数据
function saveData() {
  // 判断是否选择
  if (proTable.value.selectedList.length == 0) {
    if (props.selectAll) {
      emit('select', proTable.value.tableData);
      commonModalRef.value.visible = false;
    } else {
      return proxy.$modal.msgError('请选择数据');
    }
  } else {
    emit('select', proTable.value.selectedList);
    commonModalRef.value.visible = false;
  }
}
// 刷新tabel
function refreshCol() {
  nextTick(() => {
    proTable.value.refreshCol();
  });
}
defineExpose({
  openModal,
  refreshTable,
  refreshCol
});
</script>
