import request from '@/utils/request';
import { parseStrEmpty } from '@/utils/index';
import Cookies from 'js-cookie';
// 查询用户列表
export function listUser(data, params = {}) {
  return request({
    url: '/userInfo/s',
    method: 'post',
    data,
    params
  });
}
// 无分页
export function selectUsers() {
  return request({
    url: '/userInfo/s?page=false',
    method: 'post',
    data: {}
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + parseStrEmpty(userId),
    method: 'get'
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/userInfo',
    method: 'post',
    data
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: `/userInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除用户
export function delUser(data) {
  return request({
    url: '/userInfo/batchRemove',
    method: 'delete',
    data
  });
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password
  };
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data
  });
}
// 用户密码重置
export function resetUserPwdTwo(data) {
  return request({
    url: `/userInfo/${data.id}/password`,
    method: 'put',
    data
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status
  };
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data
  });
}
// 禁用部门
export function disableuserInfo(data) {
  return request({
    url: `/userInfo/${data.id}/disable`,
    method: 'delete'
  });
}
// 角色 批量移除用户
export function roleRemoveuserInfo(data) {
  return request({
    url: `/roleInfo/${data.id}/users`,
    method: 'delete',
    data: data.list
  });
}
// 角色 批量添加用户
export function roleBatchAdduserInfo(data) {
  return request({
    url: `/roleInfo/${data.id}/users`,
    method: 'post',
    data
  });
}
// 启用部门
export function enableuserInfo(data) {
  return request({
    url: `/userInfo/${data.id}/enable`,
    method: 'post'
  });
}
// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get'
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data
  });
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword
  };
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data
  });
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: '/system/user/authRole/' + userId,
    method: 'get'
  });
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: '/system/user/authRole',
    method: 'put',
    params: data
  });
}
// 操作日志
export function getlogInfoList(data) {
  return request({
    url: '/logInfo/s',
    method: 'post',
    data
  });
}
// 修改外观设置
export function editappearanceInfo(data) {
  return request({
    url: `/appearanceInfo/${data.id}`,
    method: 'put',
    data
  });
}
// 获取外观设置
export function getAppearanceInfo(data) {
  return request({
    url: `/appearanceInfo/s`,
    method: 'post',
    data
  });
}
// 获取外观设置
export function getAppearanceInfo2(data) {
  return request({
    url: `/appearanceInfo/getList`,
    method: 'get'
  });
}
// 上传图片
export function userInfoupload(data) {
  return request({
    url: `/userInfo/upload`,
    method: 'post',
    data
  });
}
// 站内信
export function getmessageInfo(data) {
  return request({
    url: `/messageInfo/s?page=false`,
    method: 'post',
    data
  });
}
// 站内信-已读
export function readMsg(data) {
  return request({
    url: `/messageInfo/readMsg/${data.id}`,
    method: 'post',
    data
  });
}
// 站内信-删除
export function deletemessageInfo(data) {
  return request({
    url: `/messageInfo/${data.id}`,
    method: 'delete'
  });
}
// 站内信-删除
export function getAllMessages(data) {
  return request({
    url: `/messageInfo/getMessageList/${data.id}`,
    method: 'get'
  });
}
// 登录日志
export function getloginLogList(data) {
  if (!data.type) {
    delete data.type;
  }
  return request({
    url: '/loginLog/s',
    method: 'post',
    data
  });
}
// 删除
export function delloginLog(data) {
  return request({
    url: `/loginLog/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });
}

// 修改密码
export function updatePsd(data) {
  return request({
    url: `/userInfo/${data.id}/modifyPassword`,
    method: 'put',
    data
  });
}
// 修改密码
export function roleInfoaddusers(data) {
  return request({
    url: `/roleInfo/${data.id}/users`,
    method: 'post',
    data: data.arr
  });
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: '/system/user/deptTree',
    method: 'get'
  });
}

export const getRouters = () => {
  return request({
    url: `/userInfo/${Cookies.get('userId')}/${window.allObj.platFormType}/getRouters`,
    method: 'get'
  });
};
export const getuserInfocount = () => {
  return request({
    url: `/userInfo/count`,
    method: 'get'
  });
};
export function getUnreadList(data) {
  return request({
    url: `/messageInfo/getUnread/${data.id}`,
    method: 'get'
  });
}
