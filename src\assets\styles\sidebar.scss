#app {
  .main-container {
    position: relative;
    height: 100%;

    // margin-left: $base-sidebar-width;
    transition: margin-left 0.28s;

    &-content {
      display: flex;
      justify-content: space-between;
      height: calc(100vh - 66px);

      .content-side {
        box-sizing: border-box;
        width: 240px;
        height: 100%;
        padding: 0 20px;
      }

      .content-main {
        // flex: 1;
        width: calc(100% - 260px);
        height: 100%;
        margin-right: 32px;
      }
    }
  }

  .sidebarHide {
    margin-left: 0 !important;
  }

  .sidebar-container {
    // position: fixed;
    // top: 0;
    // bottom: 0;
    // left: 0;
    z-index: 1001;

    // width: $base-sidebar-width !important;
    height: 100%;
    padding-top: 30px;
    overflow: hidden;
    font-size: 0;
    background-color: $base-menu-background;

    // box-shadow: 2px 0 6px rgb(0 21 41 / 35%);
    transition: width 0.28s;

    // reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }

    .el-scrollbar__bar.is-vertical {
      right: 0;
    }

    .el-scrollbar {
      height: 100%;
    }

    &.has-logo {
      .el-scrollbar {
        height: calc(100% - 50px);
      }
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
    }

    .el-menu {
      // width: 100% !important;
      width: 200px;
      height: 100%;
      margin: 0 auto;
      border: none;
    }

    .el-menu-item,
    .menu-title {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }

    .el-menu-item {
      height: 60px;
      font-size: 16px;
      border-radius: 9px;
    }

    .el-menu-item .el-menu-tooltip__trigger {
      display: inline-block !important;
    }

    // menu hover
    .sub-menu-title-noDropdown,
    .el-sub-menu__title {
      font-size: 18px;

      &:hover {
        background-color: rgb(0 0 0 / 6%) !important;
      }
    }

    & .theme-dark .is-active > .el-sub-menu__title {
      color: $base-menu-color-active !important;
    }

    & .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .el-sub-menu .el-menu-item {
      min-width: $base-sidebar-width !important;

      &:hover {
        // background-color: rgb(0 0 0 / 6%) !important;
        background-color: #ffffff !important;
        border-radius: 9px;
      }
    }

    & .theme-dark .nest-menu .el-sub-menu > .el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: $base-sub-menu-background !important;

      &:hover {
        // background-color: $base-sub-menu-hover !important;
        background-color: #ffffff !important;
        border-radius: 9px;
      }
    }
  }

  .el-menu--collapse .el-menu .el-sub-menu {
    min-width: $base-sidebar-width !important;
  }

  .withoutAnimation {
    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

// when menu collapsed
.el-menu--vertical {
  & > .el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }

  .nest-menu .el-sub-menu > .el-sub-menu__title,
  .el-menu-item {
    &:hover {
      color: var(--el-color-primary);

      // you can use $sub-menuHover
      // background-color: rgb(0 0 0 / 6%) !important;
      background-color: #ffffff !important;
      border-radius: 9px;
    }
  }

  // the scroll bar appears when the sub-menu is too long
  > .el-menu--popup {
    max-height: 100vh;
    overflow-y: auto;

    &::-webkit-scrollbar-track-piece {
      background: #d3dce6;
    }

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #99a9bf;
      border-radius: 20px;
    }
  }
}
