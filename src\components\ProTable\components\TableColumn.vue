<!--
 * @Author: babyyage
 * @Date: 2023-05-04 11:11:14
 * @LastEditTime: 2025-04-24 15:18:49
-->
<template>
  <RenderTableColumn v-bind="column" />
</template>

<script lang="jsx" setup name="TableColumn">
import { inject, ref, useSlots } from 'vue';
import { filterEnum, formatValue, handleProp, handleRowAccordingToProp } from '@/utils/index';
// eslint-disable-next-line no-unused-vars
const props = defineProps({
  column: {
    type: Object,
    default: () => { }
  }
});
const slots = useSlots();

const enumMap = inject('enumMap', ref(new Map()));
// 获取颜色
const getColor = (item, scope) => {
  if (!item.enum) {
    return '';
  }
  const fieldNames = { ...item.fieldNames, label: 'color' };
  return filterEnum(handleRowAccordingToProp(scope.row, item.prop), enumMap.value.get(item.prop), fieldNames, '');
};
// 渲染表格数据
const renderCellData = (item, scope) => {
  return enumMap.value.get(item.prop) && item.isFilterEnum
    ? filterEnum(handleRowAccordingToProp(scope.row, item.prop), enumMap.value.get(item.prop), item.fieldNames, '')
    : formatValue(handleRowAccordingToProp(scope.row, item.prop));
};

// 获取 tag 类型
const getTagType = (item, scope) => {
  return filterEnum(handleRowAccordingToProp(scope.row, item.prop), enumMap.value.get(item.prop), item.fieldNames, 'tag');
};
const getDot = (item, scope) => {
  if (!item.enum) {
    return '';
  }
  const fieldNames = { ...item.fieldNames, label: 'dot' };
  return filterEnum(handleRowAccordingToProp(scope.row, item.prop), enumMap.value.get(item.prop), fieldNames, '');
};
const RenderTableColumn = item => {
  return (
    <>
      {item.isShow && (
        <el-table-column
          {...item}
          align={item.align || 'center'}
          showOverflowTooltip={item.showOverflowTooltip || item.prop !== 'operation'}
        >
          {{
            default: scope => {
              if (item._children) return item._children.map(child => RenderTableColumn(child));
              if (item.render) return item.render(scope);
              if (slots[handleProp(item.prop)]) return slots[handleProp(item.prop)](scope);
              if (item.tag) return <el-tag type={getTagType(item, scope)}>{renderCellData(item, scope)}</el-tag>;
              return (
                <span class={'status-dot'} style={{ color: getColor(item, scope) }}>
                  {getDot(item, scope) == 'YES' ? <span style={{ background: getColor(item, scope) }}></span> : ''}
                  {renderCellData(item, scope)}
                </span>
              );
            },
            header: () => {
              if (item.headerRender) return item.headerRender(item);
              if (slots[`${handleProp(item.prop)}Header`]) return slots[`${handleProp(item.prop)}Header`]({ row: item });
              return item.label;
            }
          }}
        </el-table-column>
      )}
    </>
  );
};
</script>

<style lang="scss">
.status-dot {
  margin: 0 auto;

  span {
    display: inline-block;
    width: 10px;
    height: 10px;
    margin-right: 10px;
    border-radius: 50%;
  }
}
</style>
