/*
 * @Author: babyyage
 * @Date: 2024-09-18 15:33:31
 * @LastEditTime: 2024-10-24 13:36:46
 */
import request from '@/utils/request';

// 查询
export function listbadTypeInfo(data, params) {
  return request({
    url: '/stationBadInfo/s',
    method: 'post',
    data,
    params
  });
}
// 查询
export function selectbadTypeInfo(data) {
  return request({
    url: '/stationBadInfo/s?page=false',
    method: 'post',
    data
  });
}

// 新增工序
export function addbadTypeInfo(data) {
  return request({
    url: '/stationBadInfo',
    method: 'post',
    data
  });
}

// 修改工序
export function updatebadTypeInfo(data) {
  return request({
    url: `/stationBadInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除工序
export function delbadTypeInfo(data) {
  return request({
    url: `/stationBadInfo/${data}`,
    method: 'delete',
    params: { batch: true }
  });
}
