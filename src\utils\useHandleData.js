/*
 * @Author: babyyage
 * @Date: 2023-05-04 11:50:47
 * @LastEditTime: 2024-07-25 14:44:32
 */
import { ElMessageBox, ElMessage } from 'element-plus';
import i18n from '@/locales/index';
const { t } = i18n.global;
/**
 * @description 操作单条数据信息 (二次确认【删除、禁用、启用、重置密码】)
 * @param {Function} api 操作数据接口的api方法 (必传)
 * @param {Object} params 携带的操作数据参数 {id,params} (必传)
 * @param {String} message 提示信息 (必传)
 * @param {String} confirmType icon类型 (不必传,默认为 warning)
 * @returns {Promise}
 */
export const useHandleData = (api, params = {}, message, confirmType = 'warning') => {
  return new Promise((resolve, reject) => {
    ElMessageBox.confirm(`${t('general.warningIsNot')}${message}?`, t('general.warningInfo'), {
      confirmButtonText: t('general.btnconfirm'),
      cancelButtonText: t('general.btncancel'),
      type: confirmType,
      draggable: true
    }).then(async () => {
      const res = await api(params);
      if (!res) return reject(new Error(false));
      ElMessage({
        type: 'success',
        message: `${message}${t('general.warningSuccess')}!`
      });
      resolve(true);
    });
  });
};
