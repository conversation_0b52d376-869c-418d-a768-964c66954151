<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2025-06-20 10:23:46
-->
<template>
  <commonModal
    ref="commonModalRef"
    dialog-title="工单"
    confirmText="确定"
    @submit-data="saveData"
  >
    <el-form ref="ruleFormRef" label-width="100px" :model="addForm" :rules="rules">
      <el-form-item label="产品" prop="orderNo">
        <el-select
          v-model="addForm.orderNo"
          placeholder="请选择"
          filterable
          clearable
          @change="changeOrder"
        >
          <el-option
            v-for="item in productList"
            :key="item.id"
            :label="`${item.orderNo}-${item.productCode}`"
            :value="item.orderNo"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="部品番号">
        <el-input v-model="addForm.productCode" disabled />
      </el-form-item>

      <el-form-item label="物料编号">
        <el-input v-model="addForm.materialCode" disabled />
      </el-form-item>

      <el-form-item label="计划数量">
        <el-input v-model="addForm.planNumber" disabled />
      </el-form-item>
    </el-form>
  </commonModal>
</template>

<script setup>
import * as productApiList from "@/api/product/productListApi.js";
import { getCurrentInstance, reactive } from "vue";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["on-success"]);
const commonModalRef = ref();
const ruleFormRef = ref();
const rules = reactive({
  orderNo: [{ required: true, message: "请选择", trigger: "blur" }],
});
const addForm = ref({});

// 初始化弹窗
function openModal() {
  orderFn();
  commonModalRef.value.visible = true;
  proxy.resetForm("ruleFormRef");
}

//产品
const productList = ref([]);
async function orderFn() {
  productApiList.getPageApi({}, { page: false }).then((res) => {
    console.log(res,'产品')
    productList.value = res.data;
    addForm.value.orderNo = localStorage.getItem("terminal.orderCode");
    changeOrder(addForm.value.orderNo);
  });
}

const changeOrder = (val) => {
  let obj = productList.value.find((item) => item.orderNo == val);
  if (obj) {
    addForm.value.id = obj.id;
    addForm.value.productCode = obj.productCode;
    addForm.value.materialCode = obj.materialCode;
    addForm.value.planNumber = obj.planNumber;
  }
};

// 保存数据
function saveData() {
  ruleFormRef.value.validate(async (valid) => {
    if (valid) {
      await productApiList.startPageApi({
        ...addForm.value,
        lineId: localStorage.getItem("terminal.lineId"),
        stationId: localStorage.getItem("terminal.stationId"),
      });
      window.$modal.msgSuccess("操作成功");
      emit("on-success", addForm.value);
      commonModalRef.value.visible = false;
    }
  });
}
defineExpose({
  openModal,
});
</script>
