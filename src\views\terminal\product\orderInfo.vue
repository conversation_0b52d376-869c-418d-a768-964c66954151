<template>
  <div class="orderInfo">
    工单信息

    <div class="orderInfo-num">
      <div>生产单号</div>
      <div>{{ orderInfoObj.orderNo }}</div>
    </div>
    <div class="orderInfo-num">
      <div>产品编号</div>
      <div>{{ orderInfoObj.productCode }}</div>
    </div>
    <div class="orderInfo-num">
      <div>产品名称</div>
      <div>{{ orderInfoObj.productCode }}</div>
    </div>
    <div class="orderInfo-num">
      <div>计划数量</div>
      <div>{{ orderInfoObj.planNumber }}</div>
    </div>
    <div class="orderInfo-num">
      <div>报工数量</div>
      <div>{{ orderInfoObj.actualNumber }}</div>
    </div>

    <el-button type="primary" size="large" style="margin-top: 30px;width: 80%;" @click="reportBtn">报工</el-button>

    <OrderMOdal ref="OrderMOdalRef" @on-success="getOrderApiFn" />
  </div>
</template>

<script setup>
import * as getOrderListApi from '@/api/product/planApi.js'
const OrderMOdal = defineAsyncComponent(() => import('../modal/OrderMOdal.vue'));
const emit = defineEmits(['on-success']);
let orderInfoObj = ref({})
const getOrderApiFn = async () => {
  let res = await getOrderListApi.getStationOrderApi({
    stationId: localStorage.getItem('terminal.stationId')
  })
  orderInfoObj.value = res.data
  localStorage.setItem('terminal.productCode',res.data.productCode)
  emit('on-success',orderInfoObj.value);
}

//报工
let OrderMOdalRef = ref(null)
const reportBtn = () => {
  if(!localStorage.getItem('terminal.stationCode')) return window.$modal.msgError('请点击工位配置选择产线工位');
  OrderMOdalRef.value.openModal(orderInfoObj.value)
}

defineExpose({
  getOrderApiFn,
  orderInfoObj
});
</script>

<style lang="scss" scoped>
.orderInfo {
  width: 100%;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow-y: scroll;

  &-num {
    width: 80%;

    color: #fff;
    text-align: center;
    margin: 20px 0;
    font-size: 14px;
    font-weight: 700;
    height: 40px;
    line-height: 40px;
    display: flex;



    div:nth-of-type(1) {
      background: #169bd5;
      color: #fff;
      width: 75px;
      border-top-left-radius: 6px;
      border-bottom-left-radius: 6px;
    }

    div:nth-of-type(2) {
      background: #fff;
      flex: 1;
      text-align: left;
      padding-left: 10px;
      border: 1px solid #d7d7d7;
      border-top-right-radius: 6px;
      border-bottom-right-radius: 6px;
      color: #000000;
      overflow: hidden;
    }
  }
}
</style>