/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-26 11:22:19
 */
import request from '@/utils/request';

// 查询岗位列表
export function listPost(data,params={}) {
  return request({
    url: '/postInfo/s',
    method: 'post',
    data,
    params
  });
}

// 查询岗位详细
export function getPost(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  });
}

// 新增岗位
export function addPost(data) {
  return request({
    url: '/postInfo',
    method: 'post',
    data
  });
}

// 修改岗位
export function updatePost(data) {
  return request({
    url: `/postInfo/${data.id}`,
    method: 'put',
    data
  });
}
// 修改岗位
export function postInfoaddUsers(data) {
  return request({
    url: `/postInfo/${data.id}/users`,
    method: 'post',
    data: data.arr
  });
}
// 修改岗位
export function postInfodeleteUsers(data) {
  return request({
    url: `/postInfo/${data.id}/users`,
    method: 'DELETE',
    data: data.arr
  });
}

// 删除岗位
export function delPost(data) {
  return request({
    url: `/postInfo/batchRemove`,
    method: 'delete',
    data
  });
}
