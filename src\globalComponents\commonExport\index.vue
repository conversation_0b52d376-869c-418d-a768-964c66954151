<!--
 * @Author: 王雅阁 <EMAIL>
 * @Date: 2022-07-27 09:29:11
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-07-25 15:14:10
 * @FilePath: \sz-longline-ui\src\components\commonCompontents\commonExport\index.vue
-->
<template>
  <el-button type="primary" :loading="exportLoading" @click="exportInfo">{{
    props.params.exportName || $t('general.btnexport')
  }}</el-button>
</template>

<script setup name="commonExport">
import { exportFile } from '@/utils/index';
import { commonExport } from '@/api/commonExport';
const { proxy } = getCurrentInstance();
const exportLoading = ref(false);
const props = defineProps({
  params: {
    type: Object,
    default: () => {}
  }
});
function exportInfo() {
  exportLoading.value = true;
  commonExport(props.params)
    .then(res => {
      exportFile(res, `${props.params.fileName}.xls`);
      window.$modal.msgSuccess(window.$t('general.warningExportSuccess'));
    })
    .catch(() => {
      proxy.$modal.msgError(window.$t('general.warningExportError'));
    })
    .finally(() => {
      exportLoading.value = false;
    });
}
// export default {
//   components: {},
//   props: {
//     params: {
//       type: Object,
//       default: () => {}
//     }
//     // fileName: {
//     //   type: String,
//     //   default: ''
//     // }
//   },
//   data() {
//     return {
//       exportLoading: false
//     };
//   },
//   computed: {},
//   watch: {},
//   created() {},
//   mounted() {},
//   methods: {

//   }
// };
</script>
