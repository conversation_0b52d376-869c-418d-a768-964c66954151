<!--
 * @Author: babyyage
 * @Date: 2023-05-04 11:11:14
 * @LastEditTime: 2023-05-09 16:21:13
-->
<template>
  <!-- 分页组件 -->
  <el-pagination
    :background="true"
    :current-page="pageable.pageIndex"
    :page-size="pageable.pageSize"
    :page-sizes="[10, 25, 50, 100]"
    :total="pageable.total"
    layout="total, sizes, prev, pager, next, jumper"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  ></el-pagination>
</template>

<script setup name="Pagination">
defineProps({
  pageable: {
    type: Object,
    default: () => {
      return {
        pageIndex: 1,
        pageSize: 10,
        total: 0
      };
    }
  },
  handleSizeChange: {
    type: Function,
    default: () => {}
  },
  handleCurrentChange: {
    type: Function,
    default: () => {}
  }
});
</script>
