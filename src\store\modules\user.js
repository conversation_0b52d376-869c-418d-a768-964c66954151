/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-04-24 15:23:35
 */
import { login, logout, getInfo } from '@/api/login';
import { getToken, setToken, removeToken } from '@/utils/auth';
import Cookies from 'js-cookie';
// import defAva from '@/assets/images/profile.jpg';
import { defineStore } from 'pinia';
const useUserStore = defineStore('user', {
  state: () => ({
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: []
  }),
  actions: {
    // 登录
    login(userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      return new Promise((resolve, reject) => {
        login(username, password)
          .then(res => {
            Cookies.set('userId', res.data.id);
            const Authorization = 'Bearer' + res.data.Authorization;
            setToken(Authorization);
            this.token = Authorization;
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 获取用户信息
    getInfo() {
      const userId = Cookies.get('userId');
      return new Promise((resolve, reject) => {
        getInfo(userId)
          .then(res => {
            const { data } = res;
            const user = data.user;
            // const avatar = user.avatar == '' || user.avatar == null ? defAva : import.meta.env.VITE_APP_BASE_API + user.avatar;
            if (data.roles && data.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = data.roles;
              this.permissions = data.permissions;
            } else {
              this.roles = ['ROLE_DEFAULT'];
            }
            this.name = user.userName;
            // this.avatar = avatar;
            resolve(res);
          })
          .catch(error => {
            reject(error);
          });
      });
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        logout(this.token)
          .then(() => {
            this.token = '';
            this.roles = [];
            this.permissions = [];
            removeToken();
            resolve();
          })
          .catch(error => {
            reject(error);
          });
      });
    }
  }
});

export default useUserStore;
