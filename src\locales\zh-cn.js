import { zhcnJSONExt } from 'com-businessnew';
const zhobj = zhcnJSONExt || {};
export const zhcnJSON = {
  ...zhobj,
  general: {
    btnyes: '是',
    btnno: '否',
    btndetail: '详情',
    btnadd: '添加',
    btndelete: '删除',
    btnremove: '移除',
    btnedit: '编辑',
    btnmore: '更多',
    btnconfirm: '确定',
    btnoperate: '操作',
    btncancel: '取消',
    btnenable: '启用',
    btndisabled: '停用',
    btnmoveup: '上移',
    btnmovedown: '下移',
    btnback: '返回',
    btnsave: '保存',
    btnimport: '导入',
    btnexport: '导出',
    placeholderchoose: '请选择',
    placeholderinput: '请输入',
    warningInfo: '提示',
    warningIsNot: '是否',
    warningSuccess: '成功',
    warningFail: '失败',
    warningImportSuccess: '导入成功',
    warningExportSuccess: '导出成功',
    warningExportError: '导出失败',
    warningSaveSuccess: '保存成功',
    warningAddSuccess: '添加成功',
    warningEditSuccess: '修改成功',
    tablesort: '序号',
    tableremark: '备注',
    tableremarkplaceholder: '请输入备注',
    tablecreateTime: '创建日期',
    tablestatus: '状态',
    tablestatusplaceholder: '请选择状态',
    tableall: '全部',
    tableupdateUserName: '更新人',
    tableupdateTime: '更新时间'
  },
  menu: {
    ...zhobj.menuTwo,
    home: '首页',
    system: '系统管理',
    sysnotice: '系统通知',
    personalspace: '个人中心',
    workspace: '工作空间',
    sysboard: '系统看板',
    user: '用户管理',
    post: '岗位管理',
    role: '角色管理',
    dept: '部门管理',
    staffList: '员工管理',
    loginLogs: '登录日志',
    operateLogs: '操作日志',
    appearanceSet: '外观设置',
    labelList: '标签管理',
    notice: '公告管理',
    menu: '菜单管理',
    menuGroupList: '菜单组管理',
    receiver: '接收人管理',
    channel: '渠道管理',
    messageTemp: '信息模板',
    noticeRecord: '通知记录'
  },
  login: {
    welcomeinfo: '您好，欢迎登录',
    username: '账号',
    password: '密码',
    usernameplaceholder: '请输入账号',
    passwordplaceholder: '请输入密码',
    rememberMe: '记住密码',
    loginbtn: '登 录',
    loginbtn2: '登 录 中...',
    editpassword: '修改密码',
    editpasswordsuccess: '修改成功，请重新登录',
    logout: '退出登录',
    logout2: '确定注销并退出系统吗？',
    oldpsd: '原密码',
    newpsd: '新密码',
    newpsd2: '确认密码',
    oldpsdplaceholder: '请输入原密码',
    newpsdplaceholder: '请输入新密码',
    newpsd2placeholder: '请输入确认密码',
    newandnewnot: '新密码输入不一致，请重新输入'
  },
  request: {
    timeout: '系统接口请求超时',
    networkerror: '后端接口连接异常',
    networkerror2: '系统接口',
    networkerror3: '异常',
    relogin: '无效的会话，或者会话已过期，请重新登录。'
  },
  components: {
    uploadwarn1: '请上传',
    uploadsizewarn: '大小不超过',
    uploadformatwarn: '格式为',
    uploadformatwarn2: '的文件',
    uploadformatwarn3: '文件格式不正确, 仅支持',
    uploadformatwarn4: '上传文件大小不能超过',
    uploadformatwarn5: '正在上传，请稍候...',
    uploadformatwarn6: '上传文件数量不能超过',
    uploadformatwarn7: '上传失败',
    uploadchoosefile: '选择文件',
    review: '预览'
  },
  home: {
    personalSpace: {
      username: '姓名',
      userage: '年龄',
      userjobnum: '工号',
      userdept: '部门',
      usersex: '性别',
      userpost: '岗位',
      usercount: '账号',
      userstaff: '员工',
      titleworkspace: '工作空间',
      titlesysboard: '系统看板',
      titlenotice: '公告',
      titlenoticeList: '公告列表',
      titlenoticetitle: '公告标题',
      titlenoticecontent: '公告内容',
      titlenoticepublishuser: '发布账号',
      titlecmodel: '企业模型',
      titlememo: '备忘录',
      titlememoadd: '添加备忘录',
      titlememonodata: '暂无备忘录',
      noticeWelcome: '您好，欢迎您的登录使用，祝您工作顺利~',
      noticetime: '登录时间',
      publishtime: '发布时间',
      publishuser: '发布人',
      premonth: '上个月',
      today: '本日',
      nextmonth: '下个月',
      monday: '星期一',
      tuesday: '星期二',
      wednesday: '星期三',
      thursday: '星期四',
      friday: '星期五',
      saturday: '星期六',
      sunday: '星期日'
    },
    workSpace: {
      searchplaceholder: '请输入菜单名称',
      menuadd: '添加菜单',
      menuaddwarning: '请先选择！',
      menuname: '菜单名称',
      menumodule: '所属模块'
    },
    systemBoard: {
      searchplaceholder: '请输入看板名称/看板链接/备注',
      boardadd: '添加看板',
      boardedit: '编辑看板',
      boardicon: '看板图标',
      boardname: '看板名称',
      boardurl: '看板链接',
      boardiconplaceholder: '请选择看板图标',
      boardnameplaceholder: '请输入看板名称',
      boardurlplaceholder: '请输入看板链接',
      boardbtnreview: '预览'
    },
    notice: {
      publishnotice: '发布公告',
      title: '公告标题',
      content: '公告内容',
      titleplaceholder: '请输入公告标题',
      contentplaceholder: '请输入公告内容',
      reset: '重置',
      publish: '发布',
      publishrecord: '公告记录'
    }
  },
  system: {
    userInfo: {
      searchplaceholder: '请输入账号名/账号',
      userplace: '用户',
      userplaceTwo: '请选择用户',
      userInfoadd: '添加账号',
      userInfoedit: '编辑账号',
      name: '账号名',
      account: '账号',
      role: '角色',
      password: '密码',
      nameplaceholder: '请输入账号名',
      accountplaceholder: '请输入账号',
      roleplaceholder: '请选择角色',
      passwordplaceholder: '请输入密码',
      resetPsd: '重置密码',
      resetPsdSuccess: '重置成功',
      resetPsd2: '重置后密码',
      resetPsd2placeholder: '请输入重置后密码'
    },
    post: {
      searchplaceholder: '请输入名称/编号/备注',
      postadd: '添加岗位',
      postedit: '编辑岗位',
      memberadd: '添加成员',
      memberremove: '移除成员',
      name: '岗位名称',
      postCode: '岗位编号',
      membersCount: '岗位人员',
      nameplaceholder: '请输入岗位名称',
      postCodeplaceholder: '请输入岗位编号',
      membersCounttodetail: '查看岗位员工'
    },
    staff: {
      searchplaceholder: '请输入姓名/工号/手机号/部门/岗位',
      staffadd: '添加员工',
      staffedit: '编辑员工',
      name: '姓名',
      jobCode: '工号',
      gender: '性别',
      genderman: '男',
      genderwomen: '女',
      age: '年龄',
      phone: '手机号',
      deptName: '部门',
      postName: '岗位',
      nameplaceholder: '请输入姓名',
      ageplaceholder: '请输入年龄',
      jobCodeplaceholder: '请输入工号',
      phoneplaceholder: '请输入手机号',
      deptNameplaceholder: '请选择部门',
      postNameplaceholder: '请选择岗位',
      linkeduserplaceholder: '请选择关联账号',
      linkeduser: '关联账号'
    },
    role: {
      searchplaceholder: '请输入名称',
      roleadd: '添加角色',
      roleedit: '编辑角色',
      name: '角色名称',
      orderNo: '角色顺序',
      funcaccess: '功能权限',
      funcaccessrefresh: '更新权限',
      accountcontrol: '账号管理',
      accountadd: '添加账号',
      accountdelete: '移除账号'
    },
    dept: {
      searchplaceholder: '请输入名称/编号/备注',
      name: '部门名称',
      deptCode: '部门编号',
      nameplaceholder: '请输入部门名称',
      deptCodeplaceholder: '请输入部门编号',
      deptMember: '部门人员',
      memberCount: '人数'
    },
    loginLogs: {
      searchplaceholder: '请输入账号名/IP地址',
      loginterminal: '登录端',
      loginDate: '登录日期',
      ip: 'ip地址'
    },
    operateLogs: {
      searchplaceholder: '请输入名称/编号',
      operateDate: '操作日期',
      operateRemark: '操作描述',
      inputParams: '入参',
      outParams: '出参'
    },
    appearanceSet: {
      loginlogo: '管理端-登录界面LOGO',
      loginsystemName: '管理端-登录界面系统名称',
      loginbg: '管理端-登录界面背景图',
      hometitle: '管理端-系统左上角展示',
      homebg: '管理端-系统背景图',
      showimg: '显示图片',
      showname: '显示名称',
      uploadimgplace: '请上传图片'
    },
    menu: {
      menuadd: '添加菜单',
      menuedit: '编辑菜单',
      searchplaceholder: '请输入菜单名称',
      name: '菜单名称',
      groupTypeName: '所属菜单组',
      icon: '菜单图标',
      orderNo: '排序',
      perms: '权限标识',
      component: '组件路径',
      path: '路由地址',
      pathparams: '路由参数',
      parentMenu: '上级菜单',
      iscache: '是否缓存',
      isshow: '显示状态',
      isouturl: '是否外链',
      menuType: '菜单类型',
      isenable: '菜单状态',
      apiUrl: '接口地址',
      nameplaceholder: '请输入菜单名称',
      groupTypeNameplaceholder: '请选择所属菜单组',
      iconplaceholder: '请选择图标',
      orderNoplaceholder: '请输入序号',
      permsplaceholder: '请输入权限标识',
      componentplaceholder: '请输入组件路径',
      pathplaceholder: '请输入路由地址',
      pathparamsplaceholder: '请输入路由参数',
      parentMenuplaceholder: '请选择上级菜单',
      menuTypeplaceholder: '请选择菜单类型',
      apiUrlplaceholder: '请输入接口地址',
      pathtooltip1: '选择是外链则路由地址需要以`http(s)://`开头',
      pathtooltip2: '访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头',
      option_CATALOG: '目录',
      option_MENU: '菜单',
      option_BUTTON: '按钮',
      option_CACHE: '缓存',
      option_NOTCACHE: '不缓存',
      option_SHOW: '显示',
      option_HIDDEN: '隐藏',
      toggle: '展开/折叠'
    },
    menuGroup: {
      searchplaceholder: '请输入名称/编号',
      menuGroupadd: '添加菜单组',
      menuGroupedit: '编辑菜单组',
      name: '名称',
      code: '编号',
      nameplaceholder: '请输入名称',
      codeplaceholder: '请输入编号'
    }
  },
  sysnotice: {
    receiver: {
      searchplaceholder: '请输入接收人名称/账号',
      receiveradd: '添加接收人',
      receiveredit: '编辑接收人',
      receiverremove: '移除接收人',
      receiverimport: '导入接收人',
      datasource: '来源',
      name: '接收人',
      nameplaceholder: '请输入接收人名称',
      accountorname: '账号名/账号',
      channel: '通知渠道',
      channelsearchplaceholder: '请输入通知渠道/接收标识',
      channelconfig: '配置渠道',
      channelbind: '绑定渠道',
      channelbindsuccess: '绑定成功',
      channelremove: '移除渠道',
      receivetype: '接收标识',
      open: '开',
      close: '关'
    },
    channel: {
      dingding: '钉钉',
      wechat: '企业微信',
      email: '邮件',
      sysnotic: '系统通知',
      corpId: '企业ID',
      corpSecret: 'Secret',
      agentId: '应用ID',
      mailName: '邮箱地址',
      mailPassword: '邮箱授权码',
      smtp: 'SMTP主机名',
      smtpPort: '主机端口号'
    },
    messageTemp: {
      searchplaceholder: '请输入模板名称/模板编号/业务类型/模板内容',
      messageTempadd: '添加模板',
      messageTempedit: '编辑模板',
      name: '模板名称',
      code: '模板编号',
      type: '业务类型',
      format: '模板内容',
      nameplaceholder: '请输入模板名称',
      codeplaceholder: '请输入模板编号',
      typeplaceholder: '请输入业务类型',
      formatplaceholder: '请输入模板内容',
      formatplaceholder2: '{$name}测试消息模板发送{$company}'
    },
    noticeRecord: {
      searchplaceholder: '请输入接收人/业务类型/通知渠道/通知内容',
      createTime: '通知时间',
      content: '通知内容',
      resetnotice: '重发消息',
      resetnoticebtn: '重发'
    }
  }
};
