/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-07-19 11:08:53
 */
import request from '@/utils/request';

// 查询岗位列表
export function listroleType(data) {
  return request({
    url: '/roleType/s',
    method: 'post',
    data
  });
}
// 查询岗位列表
export function selectroleType(data) {
  return request({
    url: '/roleType/s?page=false',
    method: 'post',
    data
  });
}

// 新增岗位
export function addroleType(data) {
  return request({
    url: '/roleType',
    method: 'post',
    data
  });
}

// 修改岗位
export function updateroleType(data) {
  return request({
    url: `/roleType/${data.id}`,
    method: 'put',
    data
  });
}
// 修改岗位
export function roleTypedeleteUsers(data) {
  return request({
    url: `/roleType/${data.id}/users`,
    method: 'DELETE',
    data: data.arr
  });
}

// 删除岗位
export function delroleType(id) {
  return request({
    url: `/roleType/${id}?batch=true`,
    method: 'delete'
  });
}
