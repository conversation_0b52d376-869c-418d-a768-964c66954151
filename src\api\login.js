/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-07-19 11:07:12
 */
import request from '@/utils/request';

// 登录方法
export function login(username, password, code, uuid) {
  const formData = new FormData();
  formData.append('username', username);
  formData.append('password', password);
  formData.append('type', `${window.allObj.platFormType}`);
  return request({
    url: '/login',
    method: 'post',
    data: formData
  });
}

// 注册方法
export function register(data) {
  return request({
    url: '/register',
    method: 'post',
    data
  });
}

// 获取用户详细信息
export function getInfo(id) {
  return request({
    url: `/userInfo/${id}/${window.allObj.platFormType}/getInfo`,
    method: 'get'
  });
}

// 退出方法
export function logout() {
  return request({
    url: '/logout',
    method: 'post'
  });
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: '/captchaImage',
    method: 'get',
    timeout: 20000
  });
}
