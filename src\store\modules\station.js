/*
 * @Author: 方志良
 * @Date: 2024-08-26 13:32:49
 * @LastEditors: 方志良
 * @LastEditTime: 2024-08-26 14:02:14
 * @FilePath: \fronted-admin-template-develop\src\store\modules\station.js
 *
 */

import { defineStore } from 'pinia';
const useStationStore = defineStore('stationStore', {
  state: () => ({
    SOPList: [
      // {
      //   url: 'http://biweiman.pz-smart.cn/profile/upload/20240821165019_mmmm_7acca448ed6047be90be0129885d1b88.jpg'
      // },
      // {
      //   url: 'http://biweiman.pz-smart.cn/profile/upload/20240717143545_GCL-300A%E4%BD%9C%E4%B8%9A%E6%8C%87%E5%AF%BC%E4%B9%A6_862f08665ae04c4dbc5fd4abdc699dad.pdf'
      // },
      // {
      //   filePath:
      //     'http://biweiman.pz-smart.cn/profile/upload/20240604160706_%E6%99%AE%E7%BD%97%E6%96%AF_baa0c40a4deb4617b661cc9bbc20fb0d.pdf'
      // }
    ],
    autoFlag: false
  }),
  actions: {}
});

export default useStationStore;
