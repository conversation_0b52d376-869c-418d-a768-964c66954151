<!--
 * @Author: 方志良 
 * @Date: 2025-04-17 11:55:42
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-03 08:58:02
 * @FilePath: \yihe-front-end\src\views\terminal\product\AllProduct.vue
 * 
-->
<template>
  <div>
    <commonModal
      ref="commonModalRef"
      dialog-title="产品"
      popup-width="1200px"
      :footerShow="false"
    >
      <ProTable
        ref="proTable"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
      >
        <el-table-column
          label="产品名称"
          prop="productName"
          width="auto"
          align="center"
        ></el-table-column>
        <el-table-column
          label="产品编码"
          prop="productCode"
          width="auto"
          align="center"
        ></el-table-column>
        <template #operation="scope">
          <el-button type="primary" link @click="selectBtn(scope.row)">选择</el-button>
        </template>
      </ProTable>
    </commonModal>
    <ProductSop ref="ProductSopRef" @on-success="ProductSopFn" />
  </div>
</template>

<script lang="jsx" setup>
const ProductSop = defineAsyncComponent(() => import("../modal/ProductSop.vue"));
import { getCurrentInstance, nextTick } from "vue";
import * as productList from "@/api/product/productListApi.js";
const { proxy } = getCurrentInstance();
const emit = defineEmits(["on-success"]);
const commonModalRef = ref();
const proTable = ref();
const columns = [{ prop: "operation", label: "操作", fixed: "right", width: 240 }];
const getTableList = (params) => {
  const paramsAll = {
    ...params,
    orCfgs: [
      {
        name: "productCode",
        type: "like:B",
        value: params.keyword || "",
      },
    ],
  };
  return productList.getPageApi(paramsAll);
};

// 初始化弹窗
function openModal() {
  commonModalRef.value.visible = true;
  nextTick(() => {
    proTable.value.getTableList();
    proTable.value.clearSelection();
  });
}

// 选择
let ProductSopRef = ref(null);
function selectBtn(row) {
  ProductSopRef.value.openModal(row);
}

const ProductSopFn = (arr) => {
  emit("on-success", arr);
  commonModalRef.value.visible = false;
};

defineExpose({
  openModal,
});
</script>
