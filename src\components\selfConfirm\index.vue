<template>
  <el-dialog v-model="open" :title="props.title" append-to-body>
    <p style="text-align: center; width: 100%; padding: 20px; font-weight: bold; font-size: 24px">{{ content }}</p>
    <slot></slot>
    <template #footer>
      <div class="dialog-footer">
        <el-button link :style="{ color: props.closeColor }" @click="open = false">取 消</el-button>
        <el-button style="border: none" :style="{ background: props.confirmColor }" type="primary" @click="submitFileForm"
          >确 定</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="confirm">
const emit = defineEmits(['on-success']);
//    成功回调，格式，
const props = defineProps({
  title: {
    type: String,
    default: '系统提示'
  },

  confirmColor: {
    type: String,
    default: 'red'
  },
  closeColor: {
    type: String,
    default: 'green'
  }
});
const open = ref(false);
const content = ref('系统提示');
function openModal(con) {
  open.value = true;
  content.value = con;
}
function closeModal() {
  open.value = false;
}
function submitFileForm() {
  open.value = false;
  emit('on-success');
}
defineExpose({
  openModal,
  closeModal
});
</script>
