<!--
 * @Author: babyyage
 * @Date: 2024-07-02 14:49:52
 * @LastEditTime: 2025-05-27 16:18:41
-->
<template>
  <div style="height: 100%">
    <!-- <commonModal ref="commonModalRef" dialog-title="安灯操作" :footerShow="false"> -->

    <div class="andon">
      <div class="hujiao">安灯呼叫</div>
      <div class="andon-main">
        <div class="andon-main-menus">
          <div class="menutypes">
            <div
              v-for="item in typeList"
              :key="item.id"
              :class="!item.isNormal ? 'active' : ''"
              @click="tochoose(item)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- </commonModal> -->
    <ChooseYichange ref="chooseYichange" @on-success="getTypes" />
  </div>
</template>

<script setup>
import { getTypeByStation } from "@/api/home/<USER>";
import ChooseYichange from "./chooseYichange";
const typeList = ref([]);
function getTypes() {
  if (!localStorage.getItem("terminal.stationCode")) return;
  getTypeByStation(localStorage.getItem("terminal.stationCode")).then((res) => {
    typeList.value = res.data;
  });
}

const chooseYichange = ref();
function tochoose(val) {
  chooseYichange.value.openModal(val);
}
getTypes();
// 初始化弹窗
// const commonModalRef = ref();
function openModal() {
  // commonModalRef.value.visible = true;
  getTypes();
}

defineExpose({
  openModal,
});
</script>

<style lang="scss" scoped>
.andon {
  height: 100%;
  width: 100%;
  overflow-y: auto;
  border: 2px solid #89d1ff;
  border-radius: 10px;

  .hujiao {
    height: 80px;
    line-height: 80px;
    text-align: center;
    border-bottom: 2px solid #89d1ff;
    background-color: #0b4af7;
    color: #ffffff;
    font-size: 35px;
  }

  &-main {
    width: 100%;
    height: calc(100% - 80px);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #0745cb;
    overflow: auto;

    &-menus {
      width: 100%;
      height: 100%;
      padding: 0 10px;
      margin-left: 10px;
      text-align: center;

      .menutitle {
        margin-top: 30px;
        margin-bottom: 20px;
        font-size: 18px;
        font-weight: 600;
        color: var(--el-color-primary);
        text-align: left;
      }

      .menutypes {
        padding: 20px;
        div {
          font-size: 33px;
          font-weight: 700;
          height: 60px;
          margin-bottom: 10px;
          line-height: 60px;
          color: #ffffff;
          text-align: center;
          cursor: pointer;
          border: 1px solid #66c8fc;
          border-radius: 5px;
          background: linear-gradient(to bottom, #0397fc, #0453f5);

          &.active {
            color: #ffffff;
            border: 1px solid var(--el-color-danger);
            background: linear-gradient(to bottom, #fe5d5d, #fa2727);
          }
        }
      }
    }
  }
}
</style>
