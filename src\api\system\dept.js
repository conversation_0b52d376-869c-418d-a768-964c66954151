/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2023-05-12 10:17:54
 */
import request from '@/utils/request';

// 查询部门列表
export function listDept(data,params={}) {
  return request({
    url: '/deptInfo/s',
    method: 'post',
    data,
    params
  });
}

// 查询部门列表（排除节点）
export function listDeptExcludeChild(deptId) {
  return request({
    url: '/system/dept/list/exclude/' + deptId,
    method: 'get'
  });
}
// 批量添加用户
export function addBatchUsers(data) {
  return request({
    url: `/deptInfo/${data.id}/users`,
    method: 'post',
    data: data.userIds
  });
}

// 查询部门详细
export function getDept(deptId) {
  return request({
    url: '/system/dept/' + deptId,
    method: 'get'
  });
}

// 新增部门
export function addDept(data) {
  return request({
    url: '/deptInfo',
    method: 'post',
    data
  });
}

// 修改部门
export function updateDept(data) {
  return request({
    url: `/deptInfo/${data.id}`,
    method: 'put',
    data
  });
}
// 禁用部门
export function disableDept(data) {
  return request({
    url: `/deptInfo/${data.id}/disable`,
    method: 'delete'
  });
}
// 启用部门
export function enableDept(data) {
  return request({
    url: `/deptInfo/${data.id}/enable`,
    method: 'delete'
  });
}

// 删除部门
export function delDept(data) {
  return request({
    url: '/deptInfo/batchRemove',
    method: 'delete',
    data
  });
}
// 移除用户
export function removeUsers(data) {
  return request({
    url: `/deptInfo/${data.id}/users`,
    method: 'delete',
    data: data.userIds
  });
}
