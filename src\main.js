/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2025-05-06 17:29:08
 */
import { createApp } from 'vue';
import ElementPlus, { dayjs } from 'element-plus';
import '@/assets/styles/index.scss'; // global css
import 'element-plus/theme-chalk/index.css';
import App from './App';
import store from './store';
import router from './router';
import directive from './directive'; // directive
import plugins from './plugins'; // plugins
import 'virtual:svg-icons-register';
import elementIcons from '@/globalComponents/SvgIcon/svgicon';
import i18n from './locales/index';
import './permission'; // permission control
import ProTable from '@/components/ProTable';
import components from './customComponents';
import SelfSelectModal from '@/components/SelfSelectModal';
import SelfConfirm from '@/components/SelfConfirm';
import { resetForm } from '@/utils/index';
import { useHandleData } from '@/utils/useHandleData';
import request from '@/utils/request';
import { staticData } from '@/utils/staticData';
import 'com-businessnew/style';
// excel上传组件
import ImportExcel from '@/components/ImportExcel/index.vue';

// import vue3SeamlessScroll from 'vue3-seamless-scroll';
const { t, tm } = i18n.global;
window.$dayjs = dayjs;
window.$t = t;
window.$tm = tm;
window.$useHandleData = useHandleData;
window.$request = request;
window.$staticData = staticData;
const app = createApp(App);
app.use(i18n);
// 全局组件挂载
app.config.globalProperties.resetForm = resetForm;
app.component('ImportExcel', ImportExcel);
app.component('ProTable', ProTable);
app.component('SelfSelectModal', SelfSelectModal);
app.component('SelfConfirm', SelfConfirm);
app.use(components);
// app.use(vue3SeamlessScroll);
app.use(router);
app.use(store);
app.use(plugins);
app.use(elementIcons);
directive(app);
// 使用element-plus 并且设置全局的大小
app.use(ElementPlus, {});

app.mount('#app');
