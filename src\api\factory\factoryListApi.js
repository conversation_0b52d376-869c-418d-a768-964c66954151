/*
 * @Author: 方志良 
 * @Date: 2025-04-24 15:07:26
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-24 15:11:22
 * @FilePath: \yihe-front-end\src\api\factory\factoryListApi.js
 * 
 */
import request from '@/utils/request';

// 获取工厂
export const getPageApi = (data, params = {}) =>
  request({
    url: `/workShop/s`,
    method: 'post',
    data,
    params
  });

// 新增工厂
export const addPageApi = data =>
  request({
    url: `/workShop`,
    method: 'post',
    data
  });

// 修改工厂
export const editPageApi = data =>
  request({
    url: `/workShop/${data.id}`,
    method: 'put',
    data
  });

// 删除工厂
export const delPageApi = data =>
  request({
    url: `/workShop/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });

// 工厂禁用
export const stopPageApi = data =>
  request({
    url: `/workShop/${data.id}/disable`,
    method: 'delete'
  });

// 工厂启用
export const startPageApi = data =>
  request({
    url: `/workShop/${data.id}/enable`,
    method: 'post'
  });