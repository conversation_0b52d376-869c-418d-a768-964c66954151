/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-06-05 16:38:32
 */
import request from '@/utils/request';

// 查询岗位列表
export function listpersonnel(data) {
  return request({
    url: '/personnel/s',
    method: 'post',
    data
  });
}
export function listpersonnel2(data) {
  return request({
    url: '/personnel/s?page=false',
    method: 'post',
    data
  });
}

// 查询岗位详细
export function getpersonnel(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  });
}

// 新增岗位
export function addpersonnel(data) {
  return request({
    url: '/personnel',
    method: 'post',
    data
  });
}

// 修改岗位
export function updatepersonnel(data) {
  return request({
    url: `/personnel/${data.id}`,
    method: 'put',
    data
  });
}

// 删除岗位
export function delpersonnel(data) {
  return request({
    url: `/personnel/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });
}
// 禁用部门
export function disablepersonnel(data) {
  return request({
    url: `/personnel/${data.id}/disable`,
    method: 'delete'
  });
}
// 启用部门
export function enablepersonnel(data) {
  return request({
    url: `/personnel/${data.id}/enable`,
    method: 'post'
  });
}
