<!--
 * @Author: babyyage
 * @Date: 2023-05-05 11:26:55
 * @LastEditTime: 2025-05-30 11:52:00
-->
<template>
  <el-dialog
    v-bind="$attrs"
    v-model="visible"
    :title="dialogTitle"
    :width="popupWidth"
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="true"
  >
    <slot />
    <template #footer>
      <span v-if="footerShow" class="dialog-footer">
        <el-button @click="handleClose">{{ $t('general.btncancel') }}</el-button>
        <el-button type="primary" :loading="addLoading" @click="Save">{{ confirmText }}</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="commonModal">
import { ref } from 'vue';

// eslint-disable-next-line no-unused-vars
const props = defineProps({
  dialogTitle: {
    type: String,
    default: ''
  },
  modelValue: {
    type: Boolean,
    default: false
  },
  popupWidth: {
    type: String,
    default: '550px'
  },
  footerShow: {
    type: Boolean,
    default: true
  },
  closeEventSelf: {
    type: Boolean,
    default: false
  },
  confirmText:{
    type: String,
    default: '确定' 
  }
});
const visible = ref(false);
const addLoading = ref(false);
// watch(props.modelValue, (newValue, oldValue) => {
//   visible.value = newValue;
// });
const emit = defineEmits(['submit-data', 'close-modal']);
function handleClose() {
  visible.value = false;
  if (props.closeEventSelf) {
    emit('close-modal');
  }
}
function Save() {
  emit('submit-data');
}
defineExpose({
  visible,
  addLoading
});
</script>
