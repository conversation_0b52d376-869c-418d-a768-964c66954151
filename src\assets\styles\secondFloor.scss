/*
 * @Author: babyyage
 * @Date: 2024-08-02 10:21:17
 * @LastEditTime: 2025-01-02 10:18:14
 */
@import './common.scss';

.secondFloor {
  width: 1920px;
  height: 1080px;
  overflow: hidden;
  background-image: url('../../assets/images/board-bg.png');
  background-size: 100% 100%;
  transform-origin: 0 0;

  /* IE 9 */
  &-header {
    position: relative;
    height: 95px;

    .logo {
      position: absolute;
      top: 30px;
      left: 48px;

      // img {
      //   width: 192px;
      //   height: 58px;
      // }
    }

    .title {
      position: absolute;
      top: 16px;
      left: 766px;
      font-family: PangMenZhengDao;
      font-size: 64px;
      font-weight: 400;
      line-height: 56px;
      color: #ffffff;
      text-align: left;
      letter-spacing: 4px;
    }

    .time {
      position: absolute;
      top: 32px;
      right: 40px;
      font-family: 'PingFang SC';
      font-size: 32px;
      line-height: 38px;
      color: #ffffff;
      text-align: left;
    }
  }

  &-total {
    display: flex;
    justify-content: space-between;
    height: 120px;
    margin: 30px 80px 50px 102px;

    &-sub {
      box-sizing: border-box;
      width: 256px;
      height: 100%;
      padding: 12px 28px 0;
      background-image: url('../../assets/images/container-2.png');
      background-size: 100% 100%;

      .title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        &-left {
          width: 15px;
          height: 14px;
          background-image: url('../../assets/images/arrow-left.png');
          background-size: 100% 100%;
        }

        &-right {
          width: 15px;
          height: 14px;
          background-image: url('../../assets/images/arrow-rig.png');
          background-size: 100% 100%;
        }

        &-main {
          font-size: 24px;
          line-height: 28px;
          color: #ffffff;
        }
      }

      .value {
        font-size: 64px;
        font-weight: 600;
        line-height: 75px;
        text-align: center;
      }
    }
  }

  &-main {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 48px 0 50px;

    &-right {
      width: 1324px;
      height: 730px;
      background-image: url('../../assets/images/container-3.png');
      background-size: 100% 100%;

      .item-title {
        height: 60px;
        font-size: 32px;
        font-weight: 500;
        line-height: 54px;
        color: #ffffff;
        text-align: center;
      }

      .headtable {
        width: 100%;
        border-collapse: collapse;

        thead tr {
          height: 60px;
          background: #015ce5;

          th {
            font-size: 20px;
            font-weight: normal;
            color: #ffffff;
            border: 1px solid #227aff;
          }
        }
      }

      .maintable {
        width: 100%;
        margin-top: -27px;
        border-collapse: collapse;

        // min-height: 480px;
        background: transparent;

        thead {
          font-family: PangMenZhengDao;
          font-size: 24px;
          font-weight: 400;
          line-height: 24px;
          color: #09fcfc;
          text-align: center;
          visibility: hidden;
        }

        tbody {
          td {
            height: 58px;
            padding: 0;
            border: 1px solid #227aff;
          }

          div {
            padding: 4px;
            font-size: 20px;
            font-weight: 500;
            color: #ffffff;
            text-align: center;
            word-break: break-all;
          }

          tr {
            background: #0438b4;
          }

          tr:nth-child(2n) {
            background: #002682;
          }
        }
      }
    }

    &-left {
      width: 470px;

      .item {
        width: 100%;
        height: 330px;
        background-image: url('../../assets/images/container-1.png');
        background-size: 100% 100%;

        &-title {
          height: 60px;
          font-size: 32px;
          font-weight: 500;
          line-height: 54px;
          color: #ffffff;
          text-align: center;
        }

        .andonhandlerate {
          display: flex;
          justify-content: space-between;
          padding: 0 30px;
          margin-top: 20px;

          &-item {
            width: 182px;

            .chart {
              width: 100%;
              height: 182px;
              background-image: url('../../assets/images/chart-1.png');
              background-size: 100% 100%;
            }

            .title {
              font-size: 24px;
              font-weight: 500;
              line-height: 28px;
              color: #ffffff;
              text-align: center;
            }
          }
        }
      }
    }
  }
}

.warp {
  width: 100%;
  height: 580px;
  margin: 0 auto;
  overflow: hidden;

  ul {
    padding: 0;
    margin: 0 auto;
    list-style: none;

    li,
    a {
      display: block;
      display: flex;
      justify-content: space-between;
      height: 30px;
      font-size: 15px;
      line-height: 30px;
    }
  }
}
