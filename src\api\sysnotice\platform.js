/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-15 14:13:55
 */
import request from '@/utils/request';

// 查询岗位列表
export function listplatformInfo(data) {
  return request({
    url: '/platformInfo/s',
    method: 'post',
    data
  });
}

// 查询岗位详细
export function getplatformInfo(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  });
}

// 新增岗位
export function addplatformInfo(data) {
  return request({
    url: '/platformInfo',
    method: 'post',
    data
  });
}

// 修改岗位
export function updateplatformInfo(data) {
  return request({
    url: `/platformInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除岗位
export function delplatformInfo(data) {
  return request({
    url: `/platformInfo/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });
}
// 禁用部门
export function disableplatformInfo(data) {
  return request({
    url: `/platformInfo/disable/${data.id}`,
    method: 'delete'
  });
}
// 启用部门
export function enableplatformInfo(data) {
  return request({
    url: `/platformInfo/enable/${data.id}`,
    method: 'post'
  });
}
// 重发消息
export function platformInforetryDemo(data) {
  return request({
    url: `/platformInfo/retryDemo/${data.id}`,
    method: 'post'
  });
}
// 消息记录
export function getnotifyRecordsInfo(data) {
  return request({
    url: `/notifyRecordsInfo/s`,
    method: 'post',
    data
  });
}
