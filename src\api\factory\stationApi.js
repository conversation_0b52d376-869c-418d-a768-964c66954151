/*
 * @Author: 方志良 
 * @Date: 2025-04-24 13:33:08
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-25 10:24:24
 * @FilePath: \yihe-front-end\src\api\factory\stationApi.js
 * 
 */
import request from '@/utils/request';

// 查询
export function listStationApi(data, params = {}) {
  return request({
    url: '/station/s',
    method: 'post',
    data,
    params
  });
}

// 查询异常类型
export function getTypeByStation(data) {
  return request({
    url: `/task/getTypeByStation/${data}`,
    method: 'get'
  });
}

// 根据异常类型查询异常
export function getCausesByStation(data) {
  return request({
    url: `/task/getCauses/${data.code}`,
    method: 'post',
    data: { typeId: data.typeId }
  });
}

// 安灯呼叫
export function taskCallApi(data) {
  return request({
    url: `/task/call`,
    method: 'post',
    data
  });
}


// 获取工位
export const getstationListApi = (data, params = {}) =>
  request({
    url: `/station/s`,
    method: 'post',
    data,
    params
  });
