/*
 * @Author: 方志良 
 * @Date: 2025-04-24 13:33:08
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-04-25 14:56:06
 * @FilePath: \yihe-front-end\src\api\factory\line.js
 * 
 */
import request from '@/utils/request';
// 产线管理新建资源
export function add(data) {
  return request({ url: '/line', method: 'post', data });
}
// 产线管理查询资源列表,支持分页
export function list(data, params = {}) {
  return request({ url: '/line/s', method: 'post', data, params });
}
// 产线管理资源是否存在
export function existing(data) {
  return request({ url: '/line/s/existing', method: 'post', data });
}
// 产线管理批量删除资源,ids以，或 -分割
export function dels(data) {
  return request({ url: '/line/' + data, method: 'delete' });
}
// 产线管理获取单个资源
export function detail(data) {
  return request({ url: '/line/' + data, method: 'get', params: data });
}
// 产线管理修改资源
export function edit(data) {
  return request({ url: '/line/' + data.id, method: 'put', data });
}
// 产线管理删除资源
export function del(data) {
  return request({ url: '/line/' + data, method: 'delete' });
}
// 产线管理产线禁用
export function disable(data) {
  return request({ url: '/line/{id}/' + data, method: 'delete' });
}
// 产线管理产线启用
export function enable(data) {
  return request({ url: '/line/{id}/' + data, method: 'post', data });
}
