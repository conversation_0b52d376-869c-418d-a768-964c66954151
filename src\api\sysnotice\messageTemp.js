/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-10-10 15:32:33
 */
import request from '@/utils/request';

// 查询岗位列表
export function listNotifyTemplate(data) {
  return request({
    url: '/NotifyTemplate/s',
    method: 'post',
    data
  });
}

// 查询岗位详细
export function getNotifyTemplate(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  });
}

// 新增岗位
export function addNotifyTemplate(data) {
  return request({
    url: '/NotifyTemplate',
    method: 'post',
    data
  });
}

// 修改岗位
export function updateNotifyTemplate(data) {
  return request({
    url: `/NotifyTemplate/${data.id}`,
    method: 'put',
    data
  });
}

// 删除岗位
export function delNotifyTemplate(data) {
  return request({
    url: `/NotifyTemplate/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });
}
// 禁用部门
export function disableNotifyTemplate(data) {
  return request({
    url: `/NotifyTemplate/disable/${data.id}`,
    method: 'delete'
  });
}
// 启用部门
export function enableNotifyTemplate(data) {
  return request({
    url: `/NotifyTemplate/enable/${data.id}`,
    method: 'post'
  });
}
// 重发消息
export function platformInforetryDemo(data) {
  return request({
    url: `/platformInfo/retryDemo/${data.id}`,
    method: 'post'
  });
}
// 消息记录
export function getnotifyRecordsInfo(data) {
  return request({
    url: `/notifyRecordsInfo/s`,
    method: 'post',
    data
  });
}
// 删除
export function delnotifyRecordsInfo(data) {
  return request({
    url: `/notifyRecordsInfo/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });
}
