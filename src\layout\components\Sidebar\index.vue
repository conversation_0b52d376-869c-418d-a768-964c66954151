<!--
 * @Author: babyyage
 * @Date: 2023-05-24 10:35:36
 * @LastEditTime: 2024-09-05 15:34:45
-->
<template>
  <div :style="{ backgroundColor: 'transparent' }">
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="'transparent'"
        :text-color="sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor"
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route1, index) in sidebarRouters"
          :key="route1.path + index"
          :item="route1"
          :base-path="route1.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import SidebarItem from './SidebarItem';
import variables from '@/assets/styles/variables.module.scss';
// import useAppStore from '@/store/modules/app';
import useSettingsStore from '@/store/modules/settings';
import usePermissionStore from '@/store/modules/permission';

const route = useRoute();
// const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();

const sidebarRouters = computed(() => {
  const routes = permissionStore.sidebarRouters.filter(ele => !ele.hidden);
  routes.forEach(ele => {
    if (ele.children === undefined) {
      ele.children = [];
    }
    if (activeMenu.value.includes(ele.path)) {
      permissionStore.setCurrentRootPath(ele.path);
      permissionStore.setSubMenus(ele.children);
    }
  });

  return routes;
});
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
// const isCollapse = computed(() => !appStore.sidebar.opened);
const isCollapse = ref(false);
const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  // const str = path.split('/');
  // return str[str.length - 1];
  return path;
});
</script>
