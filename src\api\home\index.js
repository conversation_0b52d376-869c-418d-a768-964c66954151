/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-11-04 11:47:15
 */
import request from '@/utils/request';

// 获取车间信息
export function selectworkShop(data) {
  return request({
    url: '/workShop/s?page=false',
    method: 'post',
    data
  });
}
// 获取车间信息
export function selectline(data) {
  return request({
    url: '/line/s?page=false',
    method: 'post',
    data
  });
}
// 获取车间信息
export function selectprocessInfo(data) {
  return request({
    url: '/processInfo/s?page=false',
    method: 'post',
    data
  });
}
// 获取车间信息
export function selectprocessPartRelation(data) {
  return request({
    url: '/processPartRelation/s?page=false',
    method: 'post',
    data
  });
}
// 获取车间信息
export function selectprocessBadTypeRelation(data) {
  return request({
    url: '/processBadTypeRelation/s?page=false',
    method: 'post',
    data
  });
}
// 新建不良记录
export function addproductionBadRecord(data) {
  return request({
    url: '/productionBadRecord',
    method: 'post',
    data
  });
}
// 获取车间信息
export function selectpartInfo(data) {
  return request({
    url: '/partInfo/s?page=false',
    method: 'post',
    data
  });
}
// 获取工位信息
export function selectstation(data) {
  return request({
    url: '/station/s?page=false',
    method: 'post',
    data
  });
}
// 根据工位id查询工序
export function getOptionalByStation(data) {
  return request({
    url: `/optional/getOptionalByStation/${data}`,
    method: 'get'
  });
}
// 查询异常类型
export function getTypeByStation(data) {
  return request({
    url: `/task/getTypeByStation/${data}`,
    method: 'get'
  });
}
// 获取异常处理人
export function getHandlePerson(data) {
  return request({
    url: `/task/handle/${data}`,
    method: 'get'
  });
}
// 获取求助人
export function getseekPerson(data) {
  return request({
    url: `/task/seek/${data}`,
    method: 'get'
  });
}
// 根据异常类型查询异常
export function getCausesByStation(data) {
  return request({
    url: `/task/getCauses/${data.code}`,
    method: 'post',
    data: { typeId: data.typeId }
  });
}
// 安灯呼叫
export function taskcall(data) {
  return request({
    url: `/task/call`,
    method: 'post',
    data
  });
}
// 解除异常
export function finishCall(data) {
  return request({
    url: `/task/finishCall/${data.id}`,
    method: 'post',
    data
  });
}
// 响应异常
export function responseCall(data) {
  return request({
    url: `/task/responseCall/${data.id}`,
    method: 'post',
    data
  });
}
// 安灯求助
export function helpCall(data) {
  return request({
    url: `/task/helpCall/${data.id}`,
    method: 'post',
    data
  });
}
// “提交” 和 “返回上一步” 和 “重新开始”
export function executeStep(data) {
  return request({
    url: `/executeStep/execute`,
    method: 'post',
    data
  });
}
// “提交” 和 “返回上一步” 和 “重新开始” ---保存文件
export function executeStepsaveFilePath(data) {
  return request({
    url: `/executeStep/saveFilePath`,
    method: 'post',
    data
  });
}
// 获取生产工步信息
export function getExecuteStepB(data) {
  return request({
    url: `/executeStep/getExecuteStepB`,
    method: 'get'
  });
}

// 获取不良类型
export function getbadItemList(data) {
  return request({
    url: `/badItem/s?page=false`,
    method: 'post',
    data
  });
}
// 获取不良类型
export function getbadList(data) {
  return request({
    url: `/bad/s?page=false`,
    method: 'post',
    data
  });
}
// 不良提报
export function executeStepbad(data) {
  return request({
    url: `/executeStep/bad`,
    method: 'post',
    data
  });
}
// 不良提报--记录
export function executeStepgetBadMsg(data) {
  return request({
    url: `/executeStep/getBadMsg`,
    method: 'post',
    data
  });
}
// 不良提报--添加物料
export function executeStepadd(data) {
  return request({
    url: `/executeStep/add`,
    method: 'post',
    data
  });
}
// 不良提报--移除物料
export function executeStepremove(data) {
  return request({
    url: `/executeStep/remove`,
    method: 'post',
    data
  });
}
// 不良提报--完成维修
export function executeStepfinishRepair(data) {
  return request({
    url: `/executeStep/finishRepair`,
    method: 'post',
    data
  });
}
// 不良提报--获取投产工具
export function getOptionalByBarcode(barcode) {
  return request({
    url: `/processMaterial/getOptionalByBarcode/${barcode}`,
    method: 'get'
  });
}
// 不良提报--获取投产工具
export function getproductionBadRecordConfig() {
  return request({
    url: `/productionBadRecordConfig/1`,
    method: 'get'
  });
}
// 不良提报--完成维修
export function productionBadRecordConfig(data) {
  return request({
    url: `/productionBadRecordConfig/1`,
    method: 'put',
    data
  });
}
// 安灯-响应异常
export function taskrepairContent(data) {
  return request({
    url: `/task/repairContent`,
    method: 'post',
    data
  });
}
