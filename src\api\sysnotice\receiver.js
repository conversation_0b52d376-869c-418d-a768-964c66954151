/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-15 09:13:46
 */
import request from '@/utils/request';

// 查询岗位列表
export function listReceiverInfo(data) {
  return request({
    url: '/receiverInfo/s',
    method: 'post',
    data
  });
}

// 查询岗位详细
export function getReceiverInfo(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  });
}

// 新增岗位
export function addReceiverInfo(data) {
  return request({
    url: '/receiverInfo',
    method: 'post',
    data
  });
}

// 修改岗位
export function updateReceiverInfo(data) {
  return request({
    url: `/receiverInfo/${data.id}`,
    method: 'put',
    data
  });
}
// 修改岗位
export function importReceiver(data) {
  return request({
    url: `/receiverInfo/importReceiver`,
    method: 'post',
    data: data.ids
  });
}

// 删除岗位
export function delReceiverInfo(data) {
  return request({
    url: `/receiverInfo/${data.id}?batch=true`,
    method: 'delete'
  });
}
