<!--
 * @Author: babyyage
 * @Date: 2023-05-05 11:26:55
 * @LastEditTime: 2025-06-03 09:01:34
-->
<template>
  <div class="commonTableHeader">
    <div class="commonTableHeader-btns">
      <div v-if="!$slots.headerLeft && headerLeftShow">
        <el-button v-if="toadd != '() => {}'" v-hasPermi="[addAccess]" type="primary" @click="toadd">{{
          headerLeftAddText
        }}</el-button>
        <el-button
          v-if="todelete != '() => {}'"
          v-hasPermi="[delAccess]"
          :disabled="selectedListIds.length === 0"
          @click="todelete"
          >{{ headerLeftDelText }}</el-button
        >
        <slot name="btnList" :selected-list-ids="selectedListIds"></slot>
      </div>
      <slot v-if="$slots.headerLeft" name="headerLeft"></slot>
    </div>
    <div>
      <div class="commonTableHeader-search">
        <el-form v-if="$slots.headerSearchFormItem" :inline="true" class="search-form">
          <slot name="headerSearchFormItem"></slot>
        </el-form>
        <div v-if="!$slots.headerSearch && headerSearchShow" class="commonTableHeader-search-default">
          <el-input v-model="searchParam.keyword" :style="{ width: keywordInputWidth + 'px' }" :placeholder="placeholderText" />
          <el-button style="padding: 8px; margin: 0 6px" icon="Search" @click="search" />
          <el-button
            style="padding: 8px; margin-left: 0"
            icon="RefreshLeft"
            @click="
              clearSearchForm();
              reset();
            "
          />
        </div>
        <slot v-if="$slots.headerSearch" name="headerSearch"></slot>
      </div>
    </div>
    <!-- 定义插槽用于接受其父级组件的插槽内容 -->
  </div>
</template>

<script setup name="commonTableHeader">
// eslint-disable-next-line no-unused-vars
const props = defineProps({
  selectedListIds: {
    type: Array,
    default: () => []
  },
  searchParam: {
    type: Object,
    default: () => {}
  },
  search: {
    type: Function,
    default: () => {}
  },
  reset: {
    type: Function,
    default: () => {}
  },
  toadd: {
    type: Function,
    default: () => {}
  },
  todelete: {
    type: Function,
    default: () => {}
  },
  clearSearchForm: {
    type: Function,
    default: () => {}
  },
  headerLeftShow: {
    type: Boolean,
    default: true
  },
  headerSearchShow: {
    type: Boolean,
    default: true
  },
  headerLeftAddText: {
    type: String,
    default: '添加'
  },
  headerLeftDelText: {
    type: String,
    default: '删除'
  },
  placeholderText: {
    type: String,
    default: '请输入'
  },
  keywordInputWidth: {
    type: Number,
    default: 190
  },
  addAccess: {
    type: String,
    default: '*:*:*:*'
  },
  delAccess: {
    type: String,
    default: '*:*:*:*'
  },
  queryAccess: {
    type: String,
    default: '*:*:*:*'
  }
});
</script>
<style lang="scss">
.commonTableHeader {
  display: flex;
  place-content: center space-between;
  margin-bottom: 15px;

  &-search {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-default {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .search-form {
      .el-form-item {
        margin-right: 10px !important;
        margin-bottom: 0 !important;
      }
    }
  }
}
</style>
