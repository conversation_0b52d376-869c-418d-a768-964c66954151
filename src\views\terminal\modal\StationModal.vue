<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2025-04-25 15:03:27
-->
<template>
  <commonModal ref="commonModalRef" dialog-title="配置" @submit-data="saveData">
    <el-form ref="ruleFormRef" label-width="100px" :model="addForm" :rules="rules">
      <el-form-item label="产线" prop="lineId">
        <el-select v-model="addForm.lineId" placeholder="请选择" filterable clearable @change="getstationFn">
          <el-option v-for="item in lineList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="工位" prop="stationId">
        <el-select v-model="addForm.stationId" placeholder="请选择" filterable clearable>
          <el-option v-for="item in stationList" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
    </el-form>
  </commonModal>
</template>

<script setup>
import * as stationApiList from '@/api/factory/stationApi.js';
import * as lineListApi from '@/api/factory/line.js'
import { getCurrentInstance, reactive } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['on-success']);
const commonModalRef = ref();
const ruleFormRef = ref();
const rules = reactive({
  lineId: [{ required: true, message: '请选择产线' }],
  stationId: [{ required: true, message: '请选择工位' }],
});
const addForm = ref({});

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign(
    {},
    val
  );
  lineFn()
  addForm.value = obj;
  commonModalRef.value.visible = true;
  proxy.resetForm('ruleFormRef');
}

//产线
const lineList = ref([]);
async function lineFn(val) {
  addForm.value.lineId = null
  lineListApi.list({ status: 'ENABLE', }, { page: false }).then(res => {
    lineList.value = res.data;
  });
}
//工位
const stationList = ref([])
function getstationFn(val) {
  addForm.value.stationId = null
  stationApiList.getstationListApi({ lineId: val }, { page: false }).then(res => {
    stationList.value = res.data;
  });
}
// 保存数据
function saveData() {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      let lineObj = lineList.value.find(item => item.id == addForm.value.lineId)

      if (lineObj) {
        localStorage.setItem('terminal.lineId', lineObj.id)
        localStorage.setItem('terminal.lineName', lineObj.name)
        localStorage.setItem('terminal.lineCode', lineObj.code)
      }
      let stationObj = stationList.value.find(item => item.id == addForm.value.stationId)
      if (stationObj) {
        localStorage.setItem('terminal.stationId', stationObj.id)
        localStorage.setItem('terminal.stationName', stationObj.name)
        localStorage.setItem('terminal.stationCode', stationObj.code)
      }

      window.$modal.msgSuccess('操作成功');
      emit('on-success', lineObj?.name, stationObj?.name,);
      commonModalRef.value.visible = false;
    }
  });
}
defineExpose({
  openModal
});
</script>
