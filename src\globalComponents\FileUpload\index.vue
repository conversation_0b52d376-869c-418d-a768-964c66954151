<template>
  <div class="upload-file">
    <el-upload
      ref="fileUpload"
      multiple
      :action="uploadImgUrl"
      :before-upload="handleBeforeUpload"
      :file-list="fileList"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :on-success="handleUploadSuccess"
      :show-file-list="showFileList"
      :headers="headers"
      class="upload-file-uploader"
    >
      <!-- 上传按钮 -->
      <el-button type="primary"> {{ $t('components.uploadchoosefile') }}</el-button>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      {{ $t('components.uploadwarn1') }}
      <template v-if="fileSize">
        {{ $t('components.uploadsizewarn') }} <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        {{ $t('components.uploadformatwarn') }} <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      {{ $t('components.uploadformatwarn2') }}
    </div>

    <!-- 文件列表 -->
    <transition-group class="upload-file-list el-upload-list el-upload-list--text" name="el-fade-in-linear" tag="ul">
      <li v-for="(file, index) in fileList" :key="file.uid" class="el-upload-list__item ele-upload-list__item-content">
        <el-link :href="`${baseUrl}${file.url}`" :underline="false" target="_blank">
          <span class="el-icon-document"> {{ file.name }} </span>
        </el-link>
        <div class="ele-upload-list__item-content-action">
          <el-link :underline="false" type="danger" @click="handleDelete(index)"> {{ $t('general.btndelete') }}</el-link>
        </div>
      </li>
    </transition-group>
  </div>
</template>

<script setup name="FileUpload">
import { getToken } from '@/utils/auth';

const props = defineProps({
  modelValue: {
    type: [String, Object, Array],
    default: ''
  },
  // 数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 200
  },
  fileType: {
    type: Array,
    default: () => ['png', 'jpg', 'pdf', 'mp4']
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  },
  showFileList: {
    type: Boolean,
    default: false
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(['update:modelValue', 'on-success']);
const number = ref(0);
const uploadList = ref([]);
const baseUrl = import.meta.env.VITE_APP_BASE_API;
const uploadImgUrl = ref(baseUrl + '/sopFile/upload'); // 上传的图片服务器地址
const headers = ref({ Authorization: getToken() });
const fileList = ref([]);
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

watch(
  () => props.modelValue,
  val => {
    if (val) {
      fileList.value = val.map(ele => {
        return {
          ...ele,
          url: import.meta.env.VITE_APP_BASE_API + ele.url
        };
      });
      // let temp = 1;
      // // 首先将值转为数组
      // const list = Array.isArray(val) ? val : props.modelValue.split(',');
      // // 然后将数组转为对象数组
      // fileList.value = list.map(item => {
      //   if (typeof item === 'string') {
      //     item = { name: item, url: item };
      //   }
      //   item.uid = item.uid || new Date().getTime() + temp++;
      //   return item;
      // });
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// 上传前校检格式和大小
function handleBeforeUpload(file) {
  // 校检文件类型
  if (props.fileType.length) {
    const fileName = file.name.split('.');
    let fileExt = fileName[fileName.length - 1];
    fileExt = fileExt.toLowerCase();
    const isTypeOk = props.fileType.indexOf(fileExt) >= 0;
    if (!isTypeOk) {
      proxy.$modal.msgError(`${window.$t('components.uploadformatwarn3')}${props.fileType.join('/')}`);
      return false;
    }
  }
  // 校检文件大小
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`${window.$t('components.uploadformatwarn4')} ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading(window.$t('components.uploadformatwarn5'));
  number.value++;
  return true;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`${window.$t('components.uploadformatwarn6')} ${props.limit}!`);
}
// 上传失败
function handleUploadError() {
  proxy.$modal.msgError(window.$t('components.uploadformatwarn7'));
  proxy.$modal.closeLoading();
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.success) {
    uploadList.value.push({ name: res.data.filename, url: '/' + res.data.path });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.fileUpload.handleRemove(file);
    uploadedSuccessfully();
  }
  emit('on-success', fileList.value);
}

// 删除文件
function handleDelete(index) {
  fileList.value.splice(index, 1);
  emit('update:modelValue', fileList.value);
  emit('on-success', fileList.value);
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    fileList.value = fileList.value.map(ele => {
      let url = ele.url;
      if (url.includes(import.meta.env.VITE_APP_BASE_API)) {
        url = url.split(import.meta.env.VITE_APP_BASE_API)[1];
      }
      return {
        name: ele.name,
        url
      };
    });
    emit('update:modelValue', fileList.value);
    proxy.$modal.closeLoading();
  }
}
</script>

<style scoped lang="scss">
.upload-file-uploader {
  margin-bottom: 5px;
}

.upload-file-list .el-upload-list__item {
  position: relative;
  margin-bottom: 10px;
  line-height: 2;
  border: 1px solid #e4e7ed;
}

.upload-file-list .ele-upload-list__item-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: inherit;
}

.ele-upload-list__item-content-action .el-link {
  margin-right: 10px;
}
</style>
