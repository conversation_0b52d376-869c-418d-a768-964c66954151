/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-12 16:00:32
 */
import request from '@/utils/request';

// 查询部门列表
export function listnotice(data) {
  return request({
    url: '/notice/s',
    method: 'post',
    data
  });
}

// 新增部门
export function addnotice(data) {
  return request({
    url: '/notice',
    method: 'post',
    data
  });
}

// 修改部门
export function updatenotice(data) {
  return request({
    url: `/notice/${data.id}`,
    method: 'put',
    data
  });
}

// 删除部门
export function delnotice(data) {
  return request({
    url: `/notice/${data.id}`,
    method: 'delete',
    params: { batch: true }
  });
}
