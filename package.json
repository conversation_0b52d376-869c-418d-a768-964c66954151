{"name": "pz-admin-template", "version": "0.0.1", "description": "超越科技SOP&安灯系统", "author": "pz", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:prettier": "prettier --write \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged", "preview": "vite preview", "prepare": "husky install", "commit": "git add -A && czg && git push"}, "dependencies": {"@element-plus/icons-vue": "2.0.10", "@vueuse/core": "9.5.0", "axios": "0.27.2", "canvas": "^2.11.2", "com-businessnew": "^1.0.31", "echarts": "5.4.0", "element-plus": "2.4.4", "event-source-polyfill": "^1.0.31", "file-saver": "2.0.5", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "lodash": "^4.17.21", "moment": "^2.30.1", "nprogress": "0.2.0", "pdfh5": "^1.4.9", "pinia": "2.0.22", "screenfull": "^6.0.2", "vue": "3.3.13", "vue-i18n": "^10.0.0-beta.5", "vue-plugin-hiprint": "^0.0.56", "vue-router": "4.1.4", "vue3-seamless-scroll": "^2.0.1"}, "devDependencies": {"@commitlint/cli": "^17.6.1", "@commitlint/config-conventional": "^17.6.1", "@vitejs/plugin-vue": "4.1.0", "@vitejs/plugin-vue-jsx": "^3.0.1", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/compiler-sfc": "3.2.45", "@vue/eslint-config-standard": "^8.0.1", "babel-eslint": "^10.1.0", "cz-git": "^1.6.1", "czg": "^1.6.1", "eslint": "^8.36.0", "eslint-config-prettier": "^8.7.0", "eslint-plugin-html": "^7.1.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^9.9.0", "husky": "^8.0.0", "lint-staged": "^13.2.1", "postcss-html": "^1.5.0", "prettier": "^2.8.4", "sass": "1.56.1", "stylelint": "^15.2.0", "stylelint-config-html": "^1.1.0", "stylelint-config-prettier": "^9.0.5", "stylelint-config-recess-order": "^4.0.0", "stylelint-config-recommended-scss": "^9.0.1", "stylelint-config-recommended-vue": "^1.4.0", "stylelint-config-standard": "^30.0.1", "stylelint-config-standard-scss": "^7.0.1", "unplugin-auto-import": "0.11.4", "unplugin-vue-setup-extend-plus": "^1.0.0", "vite": "4.0.0", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-setup-extend": "0.4.0"}, "config": {"commitizen": {"path": "node_modules/cz-git"}}}