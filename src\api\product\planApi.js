/*
 * @Author: 方志良 
 * @Date: 2025-04-25 10:15:17
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-06-18 17:33:30
 * @FilePath: \yihe-front-end\src\api\product\planApi.js
 * 
 */
import request from '@/utils/request';

// 获取生产计划
export const getPageApi = (data, params = {}) =>
  request({
    url: `/productionOrderInfo/s`,
    method: 'post',
    data,
    params
  });

// 新增生产计划
export const addPageApi = data =>
  request({
    url: `/productionOrderInfo`,
    method: 'post',
    data
  });

// 修改生产计划
export const editPageApi = data =>
  request({
    url: `/productionOrderInfo/${data.id}`,
    method: 'put',
    data
  });

// 删除生产计划
export const delPageApi = data =>
  request({
    url: `/productionOrderInfo/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });


// 获取未开工+暂停生产计划
export const getNewPageApi = (data, params = {}) =>
  request({
    url: `/planInfo/getNotCompleteOrder`,
    method: 'post',
    data,
    params
  });

// 生产计划开工
export const startPageApi = data =>
  request({
    url: `/productionOrderInfo/start`,
    method: 'post',
    data
  });

// 生产计划暂停
export const pausePageApi = data =>
  request({
    url: `/productionOrderInfo/pause`,
    method: 'post',
    data
  });

// 生产计划完工
export const endPageApi = data =>
  request({
    url: `/productionOrderInfo/complete`,
    method: 'post',
    data
  });

//获取当前工位工单
export const getStationOrderApi = data =>
  request({
    url: `/planInfo/getCurrentOrder`,
    method: 'post',
    data
  });

//报工
export const reportStationOrderApi = data =>
  request({
    url: `/planInfo/reportOrder`,
    method: 'post',
    data
  });