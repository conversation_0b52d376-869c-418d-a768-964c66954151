/*
 * @Author: 方志良
 * @Date: 2024-08-21 11:04:42
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-09-18 15:16:23
 * @FilePath: \fronted-admin-template-develop\src\api\factory\processApi.js
 *
 */
import request from '@/utils/request';

// 查询
export function listProcessApi(data, params = {}) {
  return request({
    url: '/processInfo/s',
    method: 'post',
    data,
    params
  });
}

// 新增工序
export function addProcessApi(data) {
  return request({
    url: '/processInfo',
    method: 'post',
    data
  });
}

// 修改工序
export function updateProcessApi(data) {
  return request({
    url: `/processInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除工序
export function delProcessApi(data) {
  return request({
    url: `/processInfo/${data.id}`,
    method: 'delete',
    params: { batch: true }
  });
}

// SOP绑定工序查询
export function listProcessSO<PERSON>pi(data, params = {}) {
  return request({
    url: '/processSopLinkInfo/s',
    method: 'post',
    data,
    params
  });
}
