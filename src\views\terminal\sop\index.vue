<template>
  <div class="sop-card">
    <button
      type="primary"
      shape="circle"
      class="arrow-button left-button-style"
      @click="leftBtn"
    >
      &lt;
    </button>
    <button
      shape="circle"
      class="arrow-button right-button-style"
      type="primary"
      style="margin-left: 10px"
      @click="rightBtn"
    >
      &gt;
    </button>
    <div v-if="sopType == 'pdf'" style="height: 100%; overflow: hidden">
      <div id="demo" style="height: 100%"></div>
    </div>
    <div
      v-if="sopType == 'mp4'"
      style="height: 100%; overflow: hidden; text-align: center"
    >
      <video
        id="video"
        style="max-width: 100%; object-fit: contain"
        :src="iframeSrc"
        autoplay
        controls
        muted
      ></video>
    </div>
    <!-- <iframe v-if="!imgTypeList.includes(sopType)" :src="calcBlob(iframeSrc)" class="iframe" frameborder="0"></iframe> -->
    <div
      v-if="imgTypeList.includes(sopType)"
      style="height: 100%; overflow: hidden; text-align: center"
    >
      <img
        style="object-fit: contain"
        :src="iframeSrc"
        alt="图片不见了"
        class="sop-img"
      />
    </div>

    <div v-if="!iframeSrc" class="nosop">
      <!-- 暂无SOP -->
      <img style="width: 100%; height: 100%" src="@/assets/ask/excu-left.png" />
    </div>
  </div>
</template>

<script setup>
import Pdfh5 from "pdfh5";

// import { getStationSOPListApi, } from '@/api/product/stationWorkApi'

const props = defineProps({
  sopArr: {
    type: Array,
    default: () => [],
  },
  sopSwitch: {
    type: Boolean,
    default: false,
  },
});
const imgTypeList = ["png", "jpg", "jpeg", "gif", "tiff"];
const iframeSrc = ref("");
let index = 0;
const time = ref(20);
const sopType = computed(() => {
  if (!iframeSrc.value) {
    return;
  }
  // console.log(iframeSrc.value, 'iframeSrc.value')
  return iframeSrc.value.split(".").pop().toLowerCase();
});

let intervalAuto = null;
const sopList = ref([]);
watch(
  () => props.sopArr,
  () => {
    // console.log(Pdfh5, 'Pdfh5')
    index = 0;
    sopList.value = props.sopArr;
    if (sopList.value.length > 0) {
      iframeSrc.value = import.meta.env.VITE_APP_FILE_PATH + sopList.value[0].filePath;
      console.log(iframeSrc.value, "iframeSrc.value");
      nextTick(() => {
        loadPdf();
        loadVideo();
      });
    } else {
      iframeSrc.value = "";
    }
  },
  { deep: true, immediate: true }
);
const leftBtn = () => {
  index = index === 0 ? sopList.value.length - 1 : index - 1;
  iframeSrc.value = import.meta.env.VITE_APP_FILE_PATH + sopList.value[index].filePath;
  loadPdf();
};
const rightBtn = () => {
  index = index >= sopList.value.length - 1 ? 0 : index + 1;
  iframeSrc.value = import.meta.env.VITE_APP_FILE_PATH + sopList.value[index].filePath;
  loadPdf();
};
// video
function loadVideo() {
  if (sopType.value != "mp4") {
    return;
  }
  clearInterval(intervalAuto);
  intervalAuto = null;
  const video = document.getElementById("video");
  video.onended = function () {
    intervalAuto = setInterval(() => {
      rightBtn();
    }, time.value * 1000 || 20000);
  };
}

function loadPdf() {
  if (sopType.value == "pdf") {
    nextTick(() => {
      const pdfCon = new Pdfh5("#demo", {
        pdfurl: iframeSrc.value,
        pageNum: false,
        scrollEnable: true,
        zoomEnable: true,
      });

      pdfCon.scrollEnable(true);
      pdfCon.zoomEnable(true);
    });
  }
}

let autoTimer = null;
function timeChange(val) {
  console.log(val,sopList.value,'此时')
  if (val && sopList.value.length > 1) {
    clearInterval(autoTimer);
    autoTimer = setInterval(() => {
      rightBtn();
    }, 10000);
  } else {
    clearInterval(autoTimer);
  }
}

defineExpose({
  timeChange,
});
</script>

<style>
.pdfjs .viewerContainer {
  padding-bottom: 10px;
  overflow: auto !important;
}
</style>
<style lang="scss" scoped>
:deep(#demo .viewerContainer) {
  width: 100%;
  height: 100%;
}

:deep(#demo .pageContainer) {
  max-width: 100% !important;
  max-height: 100% !important;
}

:deep(#demo .pageContainer img) {
  width: 100%;
  height: 100%;
}

:deep(#demo .pageNum-num) {
  display: none;
}

.sop-card {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .sop-img {
    width: 100%;
    height: 100%;
  }
}

.left-button-style {
  position: absolute;
  top: 50%;
  left: 20px;
  z-index: 99;
}

.right-button-style {
  position: absolute;
  top: 50%;
  right: 20px;
  z-index: 99;
}

.arrow-button {
  width: 50px !important;
  height: 50px !important;
  font-size: 1em !important;
  color: #ffffff;
  cursor: pointer;
  background-color: rgb(31 45 61 / 11%);
  border: 1px solid transparent;
  border-radius: 25px;
  outline: none;
}

.arrow-button:hover {
  background-color: rgb(26 31 26);
}

.nosop {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  font-size: 30px;
  color: red;
}

.left-button-style {
  position: absolute;
  left: 20px;
  top: 50%;
  z-index: 99;
}

.right-button-style {
  position: absolute;
  right: 20px;
  top: 50%;
  z-index: 99;
}

.arrow-button {
  cursor: pointer;
  font-size: 1em !important;
  width: 50px !important;
  height: 50px !important;
  color: #fff;
  border-radius: 25px;
  background-color: rgba(31, 45, 61, 0.11);
  border: 1px solid transparent;
  outline: none;
}
</style>
