/*
 * @Author: 方志良 
 * @Date: 2025-05-06 17:20:04
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-05-06 17:21:09
 * @FilePath: \yihe-front-end\src\api\product\reachApi.js
 * 
 */
import request from '@/utils/request';

// 获取达成
export const getPageApi = (data, params = {}) =>
  request({
    url: `/productionExcel/s`,
    method: 'post',
    data,
    params
  });

// 新增达成
export const addPageApi = data =>
  request({
    url: `/productionExcel`,
    method: 'post',
    data
  });

// 修改达成
export const editPageApi = data =>
  request({
    url: `/productionExcel/${data.id}`,
    method: 'put',
    data
  });

// 删除达成
export const delPageApi = data =>
  request({
    url: `/productionExcel/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });