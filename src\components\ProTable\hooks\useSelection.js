/*
 * @Author: babyyage
 * @Date: 2023-05-04 11:16:38
 * @LastEditTime: 2023-05-04 11:16:59
 */
import { ref, computed } from 'vue';

/**
 * @description 表格多选数据操作
 * @param {String} rowKey 当表格可以多选时，所指定的 id
 * */
export const useSelection = rowKey => {
  const isSelected = ref(false);
  const selectedList = ref([]);

  // 当前选中的所有 ids 数组
  const selectedListIds = computed(() => {
    const ids = [];
    selectedList.value.forEach(item => ids.push(item[rowKey]));
    return ids;
  });

  /**
   * @description 多选操作
   * @param {Array} rowArr 当前选择的所有数据
   * @return void
   */
  const selectionChange = rowArr => {
    rowArr.length ? (isSelected.value = true) : (isSelected.value = false);
    selectedList.value = rowArr;
  };

  return {
    isSelected,
    selectedList,
    selectedListIds,
    selectionChange
  };
};
