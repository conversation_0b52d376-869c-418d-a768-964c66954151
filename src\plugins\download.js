/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-07-31 13:50:17
 */
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { saveAs } from 'file-saver';
import { getToken } from '@/utils/auth';
import errorCode from '@/utils/errorCode';
import { blobValidate } from '@/utils/index';

const baseURL = import.meta.env.VITE_APP_BASE_API;

export default {
  name(name, isDelete = true) {
    const url = baseURL + '/common/download?fileName=' + encodeURIComponent(name) + '&delete=' + isDelete;
    axios({
      method: 'get',
      url,
      responseType: 'blob',
      headers: { Authorization: getToken() }
    }).then(res => {
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        const blob = new Blob([res.data]);
        this.saveAs(blob, decodeURIComponent(res.headers['download-filename']));
      } else {
        this.printErrMsg(res.data);
      }
    });
  },
  resource(resource) {
    const url = baseURL + '/common/download/resource?resource=' + encodeURIComponent(resource);
    axios({
      method: 'get',
      url,
      responseType: 'blob',
      headers: { Authorization: getToken() }
    }).then(res => {
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        const blob = new Blob([res.data]);
        this.saveAs(blob, decodeURIComponent(res.headers['download-filename']));
      } else {
        this.printErrMsg(res.data);
      }
    });
  },
  zip(url, name) {
    const url2 = baseURL + url;
    axios({
      method: 'get',
      url2,
      responseType: 'blob',
      headers: { Authorization: getToken() }
    }).then(res => {
      const isBlob = blobValidate(res.data);
      if (isBlob) {
        const blob = new Blob([res.data], { type: 'application/zip' });
        this.saveAs(blob, name);
      } else {
        this.printErrMsg(res.data);
      }
    });
  },
  saveAs(text, name, opts) {
    saveAs(text, name, opts);
  },
  async printErrMsg(data) {
    const resText = await data.text();
    const rspObj = JSON.parse(resText);
    const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
    ElMessage.error(errMsg);
  }
};
