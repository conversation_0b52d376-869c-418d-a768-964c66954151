import { reactive, computed, toRefs } from 'vue';

/**
 * @description table 页面操作方法封装
 * @param {Function} api 获取表格数据 api 方法 (必传)
 * @param {Object} initParam 获取数据初始化参数 (非必传，默认为{})
 * @param {Boolean} isPageable 是否有分页 (非必传，默认为true)
 * @param {Function} dataCallBack 对后台返回的数据进行处理的方法 (非必传)
 * */
export const useTable = (
  api,
  initParam = {},
  isPageable = true,
  dataCallBack,
  requestError,
  handleData,
  handleDataFunc,
  columns
) => {
  const state = reactive({
    // 表格数据
    tableData: [],
    // 分页数据
    pageable: {
      // 当前页数
      pageIndex: 1,
      // 每页显示条数
      pageSize: 10,
      // 总条数
      total: 0
    },
    // 查询参数(只包括查询)
    searchParam: {
      keyword: ''
    },
    // 初始化默认的查询参数
    searchInitParam: {},
    // 总参数(包含分页和查询参数)
    totalParam: {}
  });

  /**
   * @description 分页查询参数(只包括分页和表格字段排序,其他排序方式可自行配置)
   * */
  const pageParam = computed({
    get: () => {
      return {
        pageIndex: state.pageable.pageIndex - 1,
        pageSize: state.pageable.pageSize
      };
    },
    set: newVal => {
      console.log('我是分页更新之后的值', newVal);
    }
  });

  /**
   * @description 获取表格数据
   * @return void
   * */
  const getTableList = async () => {
    try {
      // 先把初始化参数和分页参数放到总参数里面
      Object.assign(state.totalParam, initParam, isPageable ? pageParam.value : {});
      let data = await api({ ...state.searchInitParam, ...state.totalParam });
      dataCallBack && (data = dataCallBack(data));
      // state.tableData = isPageable ? data.data : data.data;
      if (handleData) {
        state.tableData = handleDataFunc(data.data);
      } else {
        state.tableData = data.data;
      }
      // 解构后台返回的分页数据 (如果有分页更新分页信息)
      const { totalCount } = data;
      isPageable && updatePageable({ pageIndex: state.pageable.pageIndex, pageSize: state.pageable.pageSize, total: totalCount });
    } catch (error) {
      requestError && requestError(error);
    }
  };

  /**
   * @description 更新查询参数
   * @return void
   * */
  const updatedTotalParam = () => {
    state.totalParam = {};
    // 处理查询参数，可以给查询参数加自定义前缀操作
    const nowSearchParam = {};
    const orCfgs = [];
    // 防止手动清空输入框携带参数（这里可以自定义查询参数前缀）
    // console.log(state.searchParam);
    for (const key in state.searchParam) {
      // * 某些情况下参数为 false/0 也应该携带参数
      if (state.searchParam[key] || state.searchParam[key] === false || state.searchParam[key] === 0) {
        nowSearchParam[key] = state.searchParam[key];
        if (key == 'keyword')
          columns.forEach(column => {
            if (column.isKeyWord) {
              orCfgs.push({ name: column.prop, type: 'like:B', value: state.searchParam.keyword });
            }
          });
      }
    }
    // console.log(orCfgs);
    Object.assign(state.totalParam, nowSearchParam, { orCfgs: orCfgs }, isPageable ? pageParam.value : {});
  };

  /**
   * @description 更新分页信息
   * @param {Object} resPageable 后台返回的分页数据
   * @return void
   * */
  const updatePageable = resPageable => {
    Object.assign(state.pageable, resPageable);
  };

  /**
   * @description 表格数据查询
   * @return void
   * */
  const search = () => {
    state.pageable.pageIndex = 1;
    updatedTotalParam();
    getTableList();
  };

  /**
   * @description 表格数据重置
   * @return void
   * */
  const reset = () => {
    state.pageable.pageIndex = 1;
    state.searchParam = {
      keyword: ''
    };
    // 重置搜索表单的时，如果有默认搜索参数，则重置默认的搜索参数
    Object.keys(state.searchInitParam).forEach(key => {
      state.searchParam[key] = state.searchInitParam[key];
    });
    updatedTotalParam();
    getTableList();
  };

  /**
   * @description 每页条数改变
   * @param {Number} val 当前条数
   * @return void
   * */
  const handleSizeChange = val => {
    state.pageable.pageIndex = 1;
    state.pageable.pageSize = val;
    getTableList();
  };

  /**
   * @description 当前页改变
   * @param {Number} val 当前页
   * @return void
   * */
  const handleCurrentChange = val => {
    state.pageable.pageIndex = val;
    getTableList();
  };

  return {
    ...toRefs(state),
    getTableList,
    search,
    reset,
    handleSizeChange,
    handleCurrentChange,
    updatedTotalParam
  };
};
