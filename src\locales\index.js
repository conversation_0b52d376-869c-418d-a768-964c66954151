/*
 * @Author: babyyage
 * @Date: 2024-07-22 14:55:44
 * @LastEditTime: 2024-07-24 14:44:34
 */
import elEnLocale from 'element-plus/es/locale/lang/en';
import elZhLocale from 'element-plus/es/locale/lang/zh-cn';
import { zhcnJSON } from './zh-cn';
import { enusJSON } from './en-us';
import { createI18n } from 'vue-i18n';

const messages = {
  'en-us': {
    ...enusJSON,
    ...elEnLocale
  },
  'zh-cn': {
    ...zhcnJSON,
    ...elZhLocale
  }
};

const localeStr = localStorage.getItem('curSysName') || 'zh-cn';
const i18n = createI18n({
  legacy: false,
  locale: localeStr, // 设置当前语言
  fallbackLocale: 'en-us', // 设置回退语言
  globalInjection: true, // 全局注册 $t 方法
  messages
});

export default i18n;
