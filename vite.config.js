/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2025-06-20 10:10:04
 */
import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import createVitePlugins from './vite/plugins';
const timestamp = new Date().getTime();
// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_ENV } = env;
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    base: VITE_APP_ENV === 'production' ? '/' : '/',
    build: {
      minify: 'esbuild',
      outDir: '超越科技sop',
      rollupOptions: {
        output: {
          // 入口文件名
          // entryFileNames: `assets/[name].${timestamp}.js`,
          // // 块文件名
          // chunkFileNames: `assets/[name]-[hash].${timestamp}.js`,
          // // 资源文件名 css 图片等等
          // assetFileNames: `assets/[name]-[hash].${timestamp}.[ext]`
          chunkFileNames: `static/js/[name]-[hash].${timestamp}.js`,
          entryFileNames: `static/js/[name]-[hash].${timestamp}.js`,
          assetFileNames: chunkInfo => {
            if (['.png', '.jpg', '.jpeg'].includes(path.extname(chunkInfo.name))) {
              return `static/[ext]/[name].${timestamp}.[ext]`;
            }
            return `static/[ext]/[name]-[hash].${timestamp}.[ext]`;
          }
        }
        // external: ['vue', 'element-plus'],
        // output: {
        //   globals: {
        //     vue: 'Vue',
        //     'element-plus': 'elementPlus'
        //   }
        // }
      }
    },
    esbuild: {
      drop: VITE_APP_ENV === 'production' ? ['console', 'debugger'] : []
    },
    plugins: createVitePlugins(env, command === 'build'),
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        '~': path.resolve(__dirname, './'),
        // 设置别名
        '@': path.resolve(__dirname, './src')
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    // vite 相关配置
    server: {
      port: 6788,
      host: true,
      open: false,
      proxy: {
        // https://cn.vitejs.dev/config/#server-proxy
        '/api': {
          target: 'http://*************:1023',
          // target: 'http://*************:1023',
          // changeOrigin: true
          rewrite: p => p.replace(/\/api/, '')
        }
      }
    },
    // fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: 'internal:charset-removal',
            AtRule: {
              charset: atRule => {
                if (atRule.name === 'charset') {
                  atRule.remove();
                }
              }
            }
          }
        ]
      }
    },
    preprocessorOptions: {
      scss: {
        additionalData: `@use "~/styles/element/index.scss" as *;`
      }
    }
  };
});
