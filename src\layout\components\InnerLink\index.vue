<!--
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2023-04-25 13:39:57
-->
<template>
  <div :style="'height:' + height">
    <iframe :id="props.iframeId" style="width: 100%; height: 100%" :src="src" frameborder="no"></iframe>
  </div>
</template>

<script setup>
const props = defineProps({
  src: {
    type: String,
    default: '/'
  },
  iframeId: {
    type: String,
    default: ''
  }
});

const height = ref(document.documentElement.clientHeight - 94.5 + 'px');
</script>
