import request from '@/utils/request';
// 生产订单信息新建资源
export function add(data) {
  return request({ url: '/orderInfo', method: 'post', data });
}
// 生产订单信息新建工单记录
export function create(data) {
  return request({ url: '/orderInfo/create', method: 'post', data });
}
// 生产订单信息工单执行
export function execute(data) {
  return request({ url: '/orderInfo/execute/' + data, method: 'get', params: data });
}
// 生产订单信息工单完工
export function finishOrder(data) {
  return request({ url: '/orderInfo/finishOrder/' + data, method: 'get', params: data });
}
// 生产订单信息看板查询执行中订单
export function getBoardOrderRecord(data) {
  return request({ url: '/orderInfo/getBoardOrderRecord', method: 'get', params: data });
}
// 生产订单信息工单查询子级机加订单
export function getOrderListById(data) {
  return request({ url: '/orderInfo/getOrderListById/' + data, method: 'get', params: data });
}
// 生产订单信息工单查询工序
export function getProcessByOrder(data) {
  return request({ url: '/orderInfo/getProcessByOrder/' + data.id, method: 'get', params: data });
}
// 生产订单信息工单查询子级采购订单
export function getPurchaseOrderListById(data) {
  return request({ url: '/orderInfo/getPurchaseOrderListById/' + data, method: 'get', params: data });
}
// 生产订单信息工单报工
export function reportOrder(data) {
  return request({ url: '/orderInfo/reportOrder', method: 'post', data });
}
// 生产订单信息查询资源列表,支持分页
export function list(data) {
  return request({ url: '/orderInfo/s', method: 'post', data });
}
// 生产订单信息资源是否存在
export function existing(data) {
  return request({ url: '/orderInfo/s/existing', method: 'post', data });
}
// 生产订单信息工单开工
export function startOrder(data) {
  return request({ url: '/orderInfo/startOrder', method: 'post', data });
}
// 生产订单信息批量删除资源,ids以，或 -分割
export function dels(data) {
  return request({ url: '/orderInfo/' + data, method: 'delete' });
}
// 生产订单信息获取单个资源
export function detail(data) {
  return request({ url: '/orderInfo/' + data, method: 'get', params: data });
}
// 生产订单信息修改资源
export function edit(data) {
  return request({ url: '/orderInfo/' + data.id, method: 'put', data });
}
// 生产订单信息删除资源
export function del(data) {
  return request({ url: '/orderInfo/' + data, method: 'delete' });
}
