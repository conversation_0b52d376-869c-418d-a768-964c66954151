/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-12 16:00:39
 */
import request from '@/utils/request';

// 查询部门列表
export function listBoard(data) {
  return request({
    url: '/kanban/s',
    method: 'post',
    data
  });
}

// 新增部门
export function addBoard(data) {
  return request({
    url: '/kanban',
    method: 'post',
    data
  });
}

// 修改部门
export function updateBoard(data) {
  return request({
    url: `/kanban/${data.id}`,
    method: 'put',
    data
  });
}

// 删除部门
export function delBoard(data) {
  return request({
    url: `/kanban/${data.id}`,
    method: 'delete',
    params: { batch: true }
  });
}
