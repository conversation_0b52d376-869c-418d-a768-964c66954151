/*
 * @Author: babyyage
 * @Date: 2024-09-18 15:33:31
 * @LastEditTime: 2024-09-18 18:24:36
 */
import request from '@/utils/request';

// 查询
export function listprocessInfo(data, params) {
  return request({
    url: '/processInfo/s',
    method: 'post',
    data,
    params
  });
}

// 新增工序
export function addprocessInfo(data) {
  return request({
    url: '/processInfo',
    method: 'post',
    data
  });
}

// 修改工序
export function updateprocessInfo(data) {
  return request({
    url: `/processInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除工序
export function delprocessInfo(data) {
  return request({
    url: `/processInfo/${data}`,
    method: 'delete',
    params: { batch: true }
  });
}
// 查询
export function listbadTypeInfo(data, params) {
  return request({
    url: '/badTypeInfo/s?page=false',
    method: 'post',
    data,
    params
  });
}
// 获取不良类型
export function getprocessBadTypeRelationList(data) {
  return request({
    url: '/processBadTypeRelation/s',
    method: 'post',
    data
  });
}
// 新增不良类型
export function addprocessBadTypeRelation(data) {
  return request({
    url: '/processBadTypeRelation',
    method: 'post',
    data
  });
}

// 删除不良类型
export function delprocessBadTypeRelation(data) {
  return request({
    url: `/processBadTypeRelation/${data}`,
    method: 'delete',
    params: { batch: true }
  });
}
// 获取零部件
export function getprocessPartRelationList(data) {
  return request({
    url: '/processPartRelation/s',
    method: 'post',
    data
  });
}
// 新增零部件
export function addprocessPartRelation(data) {
  return request({
    url: '/processPartRelation',
    method: 'post',
    data
  });
}

// 删除零部件
export function delprocessPartRelation(data) {
  return request({
    url: `/processPartRelation/${data}`,
    method: 'delete',
    params: { batch: true }
  });
}
// 查询
export function listpartInfo(data) {
  return request({
    url: '/partInfo/s?page=false',
    method: 'post',
    data
  });
}
