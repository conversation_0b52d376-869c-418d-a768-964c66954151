/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-15 16:49:47
 */
import request from '@/utils/request';

// 查询岗位列表
export function listreceiveDetail(data) {
  return request({
    url: '/receiveDetail/s',
    method: 'post',
    data
  });
}

// 查询岗位详细
export function getreceiveDetail(postId) {
  return request({
    url: '/system/post/' + postId,
    method: 'get'
  });
}
// 查询岗位详细
export function getreceiveChannels(data) {
  return request({
    url: '/receiveDetail/getBind/' + data.id,
    method: 'get'
  });
}

// 新增岗位
export function addreceiveDetail(data) {
  return request({
    url: '/receiveDetail',
    method: 'post',
    data
  });
}

// 修改岗位
export function updatereceiveDetail(data) {
  return request({
    url: `/receiveDetail/${data.id}`,
    method: 'put',
    data
  });
}

// 删除岗位
export function delreceiveDetail(data) {
  return request({
    url: `/receiveDetail/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });
}
// 禁用部门
export function disablereceiveDetail(data) {
  return request({
    url: `/receiveDetail/disable/${data.id}`,
    method: 'delete'
  });
}
// 启用部门
export function enablereceiveDetail(data) {
  return request({
    url: `/receiveDetail/enable/${data.id}`,
    method: 'post'
  });
}
// 重发消息
export function platformInforetryDemo(data) {
  return request({
    url: `/platformInfo/retryDemo/${data.id}`,
    method: 'post'
  });
}
// 重发消息
export function receiveDetailsubmitBind(data) {
  return request({
    url: `/receiveDetail/submitBind/${data.id}`,
    method: 'post',
    data: data.ids
  });
}
// 消息记录
export function getnotifyRecordsInfo(data) {
  return request({
    url: `/notifyRecordsInfo/s`,
    method: 'post',
    data
  });
}
