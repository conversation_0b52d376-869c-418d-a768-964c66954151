<!--
 * @Author: 方志良 
 * @Date: 2024-10-18 12:00:10
 * @LastEditors: 方志良 
 * @LastEditTime: 2025-05-06 17:27:56
 * @FilePath: \yihe-front-end\src\components\importExcel\index.vue
 * 
-->
<template>
  <commonModal ref="commonModalRef" :dialog-title="title" @submit-data="saveData">
    <el-upload ref="uploadRef" :limit="limit" accept=".xlsx, .xls" :headers="upload.headers" :http-request="handleFiles"
      :action="url" :disabled="upload.isUploading" :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess"
      :on-error="handleFilesError" :auto-upload="false" drag>
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
      <template #tip>
        <div class="el-upload__tip text-center">
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link type="primary" v-if="downModal" :underline="false" style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate">下载模板</el-link>
        </div>
      </template>
    </el-upload>
  </commonModal>
</template>

<script setup>
import { getToken } from '@/utils/auth';
import request from '@/utils/request';
const props = defineProps({
  // 数量限制
  limit: {
    type: Number,
    default: 1,
  },
  url: {
    type: String,
    default: '#'
  },
  requestUrl: {
    type: String,
    default: ""
  },
  downModal: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: "导入"
  },
  appendArr: {
    type: Array,
    default: () => []
  }
});
const { proxy } = getCurrentInstance();
const commonModalRef = ref();

//导入
const upload = reactive({
  isUploading: false,  // 是否禁用上传
  headers: { Authorization: `Bearer ${getToken()}` },  // 设置上传的请求头部
});

const emit = defineEmits(['on-success', 'importTemplateEmits']);

/** 文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true;
  commonModalRef.value.addLoading = true
};

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  commonModalRef.value.visible = false;
  upload.isUploading = false;
  proxy.$refs.uploadRef.clearFiles();
  commonModalRef.value.addLoading = false;
};

//上传失败
const handleFilesError = (response, file, fileList) => {
  commonModalRef.value.addLoading = false;
  upload.isUploading = false;
  window.$modal.msgError('上传失败');
}


let uploadTimer = ref(null)
const handleFiles = async (fileList) => {
  let requestFun = data => {
    return request({
      url: props.requestUrl,
      method: 'post',
      data
    });
  }
  clearTimeout(uploadTimer.value) // 清除定时器
  uploadTimer.value = setTimeout(async () => { //写一个定时器 防止多次请求接口
    const fileData = new FormData() // 创建一个FormData实例
    fileData.append('file', fileList.file)  // map便利多个文件
    props.appendArr.forEach((item => {
      fileData.append(item.name, item.data)  // map便利多个文件
    }))
    await requestFun(fileData)
    window.$modal.msgSuccess('上传成功');
    emit('on-success')
  }, 800)
}

// 下载模板
const importTemplate = () => {
  emit('importTemplateEmits');
}

function openModal() {
  commonModalRef.value.visible = true;
}

const saveData = async () => {
  proxy.$refs.uploadRef.submit();
}

let uploadRef = ref()
defineExpose({
  uploadRef,
  openModal
})
</script>

<style lang="scss" scoped></style>