/*
 * @Author: 方志良
 * @Date: 2024-08-21 15:59:14
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2024-09-19 16:45:25
 * @FilePath: \fronted-admin-template-develop\src\api\SOP\SopApi.js
 *
 */
import request from '@/utils/request';

// 查询
export function listSOPApi(data, params = {}) {
  return request({
    url: '/sopFile/s',
    method: 'post',
    data,
    params
  });
}
// 查询
export function listSOPApi2(data, params = {}) {
  return request({
    url: '/sopFile/s?page=false',
    method: 'post',
    data,
    params
  });
}

// 新增SOP
export function addSOPApi(data) {
  return request({
    url: '/sopFile',
    method: 'post',
    data
  });
}

// 修改SOP
export function updateSOPApi(data) {
  return request({
    url: `/sopFile/${data.id}`,
    method: 'put',
    data
  });
}

// 删除SOP
export function delSOPApi(data) {
  return request({
    url: `/sopFile/${data.id}`,
    method: 'delete',
    params: { batch: true }
  });
}

// 禁用SOP
export function disablelSOPApi(data) {
  return request({
    url: `/sopFile/${data.id}/disable`,
    method: 'delete'
  });
}

// 启用SOP
export function enableSOPApi(data) {
  return request({
    url: `/sopFile/${data.id}/enable`,
    method: 'post'
  });
}
