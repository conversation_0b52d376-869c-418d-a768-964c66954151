/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-12 16:31:06
 */
import request from '@/utils/request';

// 查询部门列表
export function listworkSpace(data) {
  return request({
    url: '/workSpace/s',
    method: 'post',
    data
  });
}

// 新增部门
export function addworkSpace(data) {
  return request({
    url: `/workSpace/batchCreate/${data.userId}`,
    method: 'post',
    data: data.menuIds
  });
}
// 新增部门
export function moveDown(data) {
  return request({
    url: `/workSpace/moveDown/${data.id}`,
    method: 'post'
  });
}
// 新增部门
export function moveUp(data) {
  return request({
    url: `/workSpace/moveUp/${data.id}`,
    method: 'post'
  });
}

// 修改部门
export function updateworkSpace(data) {
  return request({
    url: `/workSpace/${data.id}`,
    method: 'put',
    data
  });
}

// 删除部门
export function delworkSpace(data) {
  return request({
    url: `/workSpace/${data.id}`,
    method: 'delete',
    params: { batch: true }
  });
}
// 删除部门
export function getMenus(data) {
  return request({
    url: `/workSpace/getMenu/` + data.id,
    method: 'get'
  });
}
