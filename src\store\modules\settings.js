/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2023-06-02 09:19:02
 */
import defaultSettings from '@/settings';
import { useDynamicTitle } from '@/utils/dynamicTitle';
import { defineStore } from 'pinia';
const { sideTheme, showSettings, topNav, tagsView, theme, fixedHeader, sidebarLogo, dynamicTitle, breadcrumbShow } =
  defaultSettings;

const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || '';

const useSettingsStore = defineStore('settings', {
  state: () => ({
    title: '',
    theme: storageSetting.theme || theme, // 默认主题色
    sideTheme: storageSetting.sideTheme || sideTheme,
    showSettings,
    topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
    breadcrumbShow: storageSetting.breadcrumbShow === undefined ? breadcrumbShow : storageSetting.breadcrumbShow,
    tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
    fixedHeader: storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,
    sidebarLogo: storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,
    dynamicTitle: storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle
  }),
  actions: {
    // 修改布局设置
    changeSetting(data) {
      const { key, value } = data;
      if (Object.prototype.hasOwnProperty.call(this, key)) {
        this[key] = value;
      }
    },
    // 设置网页标题
    setTitle(title) {
      this.title = title;
      useDynamicTitle();
    }
  }
});

export default useSettingsStore;
