<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2024-11-04 12:01:47
-->
<template>
  <commonModal ref="commonModalRef" dialog-title="处理异常" @submit-data="saveData">
    <el-form ref="ruleFormRef" label-width="100px" :model="addForm" :rules="rules">
      <el-form-item label="异常描述" prop="description">
        <el-input v-model="addForm.description" placeholder="请输入异常描述" />
      </el-form-item>
      <el-form-item label="处理措施" prop="repairContent">
        <el-input v-model="addForm.repairContent" placeholder="请输入处理措施" />
      </el-form-item>
    </el-form>
  </commonModal>
</template>

<script setup>
import { taskrepairContent } from '@/api/home/<USER>';
const commonModalRef = ref();
const emit = defineEmits(['on-success']);
const ruleFormRef = ref();
const rules = reactive({
  description: [{ required: false, message: '请输入异常描述' }],
  repairContent: [{ required: false, message: '请输入处理措施' }]
});
const addForm = ref({});
const { proxy } = getCurrentInstance();
// 初始化弹窗
function openModal(val) {
  commonModalRef.value.visible = true;
  addForm.value = {
    description: '',
    repairContent: '',
    causeId: val.causeId,
    code: val.code,
    typeId: val.typeId
  };
  proxy.resetForm('ruleFormRef');
}

// 保存数据
function saveData() {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      taskrepairContent({ ...addForm.value }).then(res => {
        window.$modal.msgSuccess('操作成功');
        emit('on-success');
        commonModalRef.value.visible = false;
      });
    }
  });
}
defineExpose({
  openModal
});
</script>
<style lang="scss">
.chooseContainer {
  display: flex;
  justify-content: space-between;

  &-left {
    width: 200px;

    div {
      padding: 0 6px;
      line-height: 40px;
      cursor: pointer;
      border-right: 3px solid #ffffff;
    }

    div.active {
      background-color: #f5f7fa;
      border-right: 3px solid var(--el-color-primary);
    }
  }

  &-right {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
    justify-content: space-between;
    padding: 40px;
    margin-left: 2px;
    border-left: 2px solid #dcdfe6;

    .stname {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 110px;
      margin-bottom: 30px;
      font-size: 28px;
      color: var(--el-color-primary);
      text-align: center;
      cursor: pointer;
      border: 1px solid var(--el-color-primary);
      border-radius: 10px;
    }
  }
}
</style>
