/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-04-10 13:48:05
 */
import request from '@/utils/request';

// 查询岗位列表
export function listprintTemplate(data) {
  return request({
    url: '/printTemplate/s',
    method: 'post',
    data
  });
}

// 查询岗位详细
export function getprintTemplate(postId) {
  return request({
    url: '/printTemplate/' + postId,
    method: 'get'
  });
}

// 新增岗位
export function addprintTemplate(data) {
  return request({
    url: '/printTemplate',
    method: 'post',
    data
  });
}

// 修改岗位
export function updateprintTemplate(data) {
  return request({
    url: `/printTemplate/${data.id}`,
    method: 'put',
    data
  });
}
// 修改岗位
export function postInfoaddUsers(data) {
  return request({
    url: `/printTemplate/${data.id}/users`,
    method: 'post',
    data: data.arr
  });
}
// 修改岗位
export function postInfodeleteUsers(data) {
  return request({
    url: `/printTemplate/${data.id}/users`,
    method: 'DELETE',
    data: data.arr
  });
}

// 删除岗位
export function delprintTemplate(data) {
  return request({
    url: `/printTemplate/${data.id}?batch=true`,
    method: 'delete'
  });
}
