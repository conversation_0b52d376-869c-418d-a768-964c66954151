<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2025-06-03 08:59:16
-->
<template>
  <commonModal ref="commonModalRef" dialog-title="SOP选择" popup-width="800px" @submit-data="saveData">
    <ProTable ref="proTable" :columns="columns" :request-api="getTableList" :request-auto="false">
      <template #operation="scope">
        <el-button type="primary" link @click="previewFn(scope.row)">预览</el-button>
      </template>
    </ProTable>
  </commonModal>
</template>

<script lang="jsx" setup>
import { getCurrentInstance, nextTick } from 'vue';
import * as productList from '@/api/product/productListApi.js';
const { proxy } = getCurrentInstance();

const commonModalRef = ref();
const proTable = ref();
const columns = [
  { type: 'selection', fixed: 'left', width: 60 },
  { prop: 'fileName', label: '文件名称' },
  { prop: 'updateTime', label: '修改时间' },
  { prop: 'operation', label: '操作', fixed: 'right', width: 240 }
];

const getTableList = params => {
  const paramsAll = {
    ...params,
    productId: productObj.value.id,
    orCfgs: [
      {
        name: 'fileName',
        type: 'like:B',
        value: params.keyword || ''
      }
    ]
  };
  return productList.getSOPPageApi(paramsAll, { page: false });
};

// 初始化弹窗
let productObj = ref({})
function openModal(val) {
  commonModalRef.value.visible = true;
  productObj.value = val
  nextTick(() => {
    proTable.value.getTableList();
    proTable.value.clearSelection();
  });
}

// 保存数据

function saveData() {
  if (proTable.value.selectedListIds == 0) return proxy.$modal.msgError('请选择');
  console.log(proTable.value.selectedList, '选择得SOP')
  emit('on-success', proTable.value.selectedList);
  commonModalRef.value.visible = false;
  window.$modal.msgSuccess('操作成功');
}


// 预览
const emit = defineEmits(['on-success']);
const previewFn = row => {
  const url = import.meta.env.VITE_APP_BASE_API + row.filePath;
  window.open(url, '_blank');
};

defineExpose({
  openModal
});
</script>
