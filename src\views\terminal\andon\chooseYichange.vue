<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2024-12-05 09:43:56
-->
<template>
  <div>
    <commonModal
      ref="commonModalRef"
      dialog-title="选择异常名称"
      :footer-show="false"
      popup-width="1000px"
      :close-event-self="true"
      @close-modal="closeModal"
    >
      <ProTable
        ref="proTable"
        :columns="columns"
        :request-api="getTableList"
        :request-auto="false"
        :header-left-show="false"
        :header-search-show="false"
      >
        <!-- 表格操作 -->
        <template #operation="scope">
          <el-button v-if="scope.row.status === 'FINISHED'" type="primary" link @click="tocall(scope.row)">确定呼叫</el-button>
          <el-button v-if="scope.row.status === 'CALLED'" type="primary" link @click="toxiangying(scope.row)">响应异常</el-button>
          <el-button
            v-if="scope.row.status === 'RESPONDED' && !scope.row.isRepaired"
            type="primary"
            link
            @click="tohandle(scope.row)"
            >处理</el-button
          >
          <el-button v-if="scope.row.status === 'RESPONDED'" type="primary" link @click="tojiechu(scope.row)">解除异常</el-button>
          <!-- <el-button v-if="scope.row.status === 'RESPONDED'" type="primary" link @click="tohelp(scope.row)">安灯求助</el-button> -->
        </template>
      </ProTable>
    </commonModal>
    <HelpYichang ref="helpYichang" @on-success="subCompAccess" />
    <RebindYichang ref="rebindYichang" @on-success="subCompAccess" />
    <ResYichang ref="resYichang" @on-success="subCompAccess" />
    <HandleModal ref="handleModal" @on-success="subCompAccess" />
  </div>
</template>

<script setup>
import { getCausesByStation, taskcall } from '@/api/home/<USER>';
import { nextTick } from 'vue';
import HelpYichang from './helpYichang';
import RebindYichang from './rebindYichang';
import ResYichang from './resYichang';
import HandleModal from './HandleModal';
const commonModalRef = ref();
const emit = defineEmits(['on-success']);
const columns = [
  { prop: 'name', label: '异常名称' },
  { prop: 'handle', label: '常见处理方式' },
  { prop: 'operation', label: '操作', fixed: 'right', width: 220 }
];
const proTable = ref();
function refreshTable() {
  proTable.value.getTableList();
}
const currentType = ref({});
// 初始化弹窗
function openModal(val) {
  commonModalRef.value.visible = true;
  currentType.value = val;
  nextTick(() => {
    refreshTable();
  });
}
// 加载数据
const getTableList = params => {
  return getCausesByStation({ ...params, code: localStorage.getItem('terminal.stationCode'), typeId: currentType.value.id });
};
async function tocall(row) {
  const lineInd = localStorage.getItem('terminal.lineId');
  if (!lineInd || lineInd === 'undefined' || lineInd === "'undefined'") {
    window.$modal.msgError('产线不存在，请重新配置工位信息');
    return;
  }
  await window.$useHandleData(
    taskcall,
    {
      causeId: row.id,
      code: localStorage.getItem('terminal.stationCode'),
      lineId: localStorage.getItem('terminal.lineId'),
      shiftId: row.shiftId,
      typeId: row.typeId,
      workshopId: row.workshopId,
      productCode: localStorage.getItem('terminal.productCode'),
      _extend_: {
        productCode: localStorage.getItem('terminal.productCode'),
        // optionalId: localStorage.getItem('processId'),
        stationId: localStorage.getItem('terminal.stationId')
      }
    },
    '呼叫'
  );
  proTable.value.getTableList();
  subCompAccess();
}
const helpYichang = ref();
const rebindYichang = ref();
const resYichang = ref();
function toxiangying(row) {
  resYichang.value.openModal({
    causeId: row.id,
    code: localStorage.getItem('terminal.stationCode'),
    lineId: localStorage.getItem('terminal.lineId'),
    shiftId: row.shiftId,
    typeId: row.typeId,
    workshopId: row.workshopId
  });
}
function tojiechu(row) {
  rebindYichang.value.openModal({
    causeId: row.id,
    code: localStorage.getItem('terminal.stationCode'),
    lineId: localStorage.getItem('terminal.lineId'),
    shiftId: row.shiftId,
    typeId: row.typeId,
    workshopId: row.workshopId
  });
}
const handleModal = ref();
function tohandle(row) {
  handleModal.value.openModal({
    causeId: row.id,
    code: localStorage.getItem('terminal.stationCode'),
    typeId: row.typeId
  });
}
function subCompAccess() {
  commonModalRef.value.visible = false;
  emit('on-success');
}
function closeModal() {
  emit('on-success');
}
defineExpose({
  openModal
});
</script>
