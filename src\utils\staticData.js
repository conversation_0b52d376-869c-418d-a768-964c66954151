/*
 * @Author: babyyage
 * @Date: 2023-11-21 17:07:32
 * @LastEditTime: 2024-07-26 17:04:00
 */
import i18n from '@/locales/index';
const { t } = i18n.global;
// 是、否通用
const boolList = [
  {
    value: '0',
    label: t('general.btnyes')
  },
  {
    value: '1',
    label: t('general.btnno')
  }
];

// 启用、停用通用
export const enabledList = [
  {
    value: 'ENABLE',
    label: '启用',
    dot: "YES",
    color: "#4bb555",
  },
  {
    value: 'DISABLE',
    label: '禁用',
    dot: "YES",
    color: "#f8544b",
  }
];
export const staticData = {
  enabledList,
  boolList
};

export const purcharOrderStatus = [
  {
    value: 'UNDONE',
    label: '未完成'
  },
  {
    value: 'FINISH',
    label: '已完成'
  }
];
// export const orderStatus = [
//   {
//     value: 'INIT',
//     label: '未执行'
//   },
//   {
//     value: 'TODO',
//     label: '未开工'
//   },
//   {
//     value: 'START',
//     label: '已开工'
//   },
//   {
//     value: 'FINISH',
//     label: '已完工'
//   }
// ];
export const orderStatus = [
  {
    value: 'INIT',
    label: '未执行'
  },
  {
    value: 'TODO',
    label: '未开工'
  },
  {
    value: 'EXECUTE',
    label: '已开工'
  },
  {
    value: 'END',
    label: '已完工'
  }
];
export const orderType = [
  {
    value: 'FINISH',
    label: '生产订单'
  },
  {
    value: 'WORK',
    label: '机加订单'
  },
  {
    value: 'PURCHASE',
    label: '外购订单'
  }
];
// FINISH: '成品',
//   PURCHASE: '外购品',
//   WORK: '自制品'

export const productType = [
  {
    value: 'FINISH',
    label: '成品'
  },
  {
    value: 'PURCHASE',
    label: '外购品'
  },
  {
    value: 'WORK',
    label: '自制品'
  }
];
export const produceBoardStatus = [
  {
    value: 'UNDO',
    label: '未完成'
  },
  {
    value: 'FINISH',
    label: '已完成'
  }
];
