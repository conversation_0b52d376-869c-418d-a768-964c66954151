<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2024-07-25 14:48:41
-->
<template>
  <el-drawer v-model="drawer" title="站内通知" :before-close="handleClose" :close-on-click-modal="false">
    <div class="sysnotmodal">
      <div v-for="(item, key) in dataList" :key="key" class="sysnotmodalmain">
        <div class="sysnotmodalmain-time">
          {{ item.createTime }}
        </div>
        <div class="sysnotmodalmain-content">
          {{ item.content }}
        </div>
        <div class="sysnotmodalmain-ops">
          <el-button v-if="item.status === 'UNREAD'" type="primary" link @click="toRead(item.id)">已读</el-button>
          <el-button v-if="item.status !== 'UNREAD'" type="danger" link @click="toDelete(item.id)">删除</el-button>
        </div>
      </div>
    </div>
  </el-drawer>
  <!-- <commonModal ref="commonModalRef" popup-width="800px" :footer-show="false" dialog-title="站内通知">
    <div class="sysnotmodal">
      <div v-for="(item, key) in dataList" :key="key" class="sysnotmodalmain">
        <div class="sysnotmodalmain-time">
          {{ item.time }}
        </div>
        <div class="sysnotmodalmain-content">
          {{ item.content }}
        </div>
        <div class="sysnotmodalmain-ops">
          <el-button type="primary" link>已读</el-button>
          <el-button type="danger" link>删除</el-button>
        </div>
      </div>
    </div>
  </commonModal> -->
</template>

<script setup>
import Cookies from 'js-cookie';
import { getAllMessages, readMsg, deletemessageInfo } from '@/api/system/user';
const drawer = ref(false);
const dataList = ref([]);
// 初始化弹窗
function openModal() {
  drawer.value = true;
  getList();
}
function getList() {
  getAllMessages({ id: Cookies.get('userId') }).then(res => {
    dataList.value = res.data;
  });
}
function handleClose() {
  drawer.value = false;
}
// 已读
const toRead = async id => {
  await window.$useHandleData(readMsg, { id }, '已读');
  getList();
};
// 删除
const toDelete = async id => {
  await window.$useHandleData(deletemessageInfo, { id }, '删除');
  getList();
};
defineExpose({
  openModal
});
</script>
<style lang="scss">
.sysnotmodal {
  .sysnotmodalmain {
    padding: 20px;
    margin-bottom: 20px;
    background-color: rgb(247 248 249);
    border: none;
    border-radius: 10px;

    &-time {
      font-size: 16px;
      font-weight: 700;
      color: rgb(17 17 17);
    }

    &-content {
      margin: 20px 0;
    }

    &-ops {
      text-align: right;
    }
  }
}
</style>
