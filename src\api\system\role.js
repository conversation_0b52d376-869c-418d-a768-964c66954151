import request from '@/utils/request';

// 查询角色列表
export function listRole(data, params = {}) {
  return request({
    url: '/roleInfo/s',
    method: 'post',
    data,
    params
  });
}
// 查询角色列表
export function listroleType(data) {
  return request({
    url: '/roleType/s?page=false',
    method: 'post',
    data,
  });
}

// 查询角色详细
export function getRole(roleId) {
  return request({
    url: '/system/role/' + roleId,
    method: 'get'
  });
}

// 新增角色
export function addRole(data) {
  return request({
    url: '/roleInfo',
    method: 'post',
    data
  });
}

// 修改角色
export function updateRole(data) {
  return request({
    url: `/roleInfo/${data.id}`,
    method: 'put',
    data
  });
}
// 修改角色
export function updateRole2(data) {
  return request({
    url: `roleInfo/modifyRole/${data.id}`,
    method: 'put',
    data
  });
}
// 获取角色信息
export function getRoleById(data) {
  return request({
    url: `/roleInfo/${data.id}`,
    method: 'get'
  });
}
// 添加成员
export function addUsers(data) {
  return request({
    url: `/roleInfo/${data.id}/users`,
    method: 'post',
    data
  });
}
// 获取角色信息详情
export function getRolePersById(data) {
  return request({
    url: `/roleInfo/${data.id}/${data.type}/perms`,
    method: 'get'
  });
}

// 角色数据权限
export function dataScope(data) {
  return request({
    url: '/system/role/dataScope',
    method: 'put',
    data
  });
}

// 角色状态修改
export function changeRoleStatus(roleId, status) {
  const data = {
    roleId,
    status
  };
  return request({
    url: '/system/role/changeStatus',
    method: 'put',
    data
  });
}

// 删除角色
export function delRole(data) {
  return request({
    url: '/roleInfo/batchRemove',
    method: 'delete',
    data
  });
}

// 查询角色已授权用户列表
export function allocatedUserList(query) {
  return request({
    url: '/system/role/authUser/allocatedList',
    method: 'get',
    params: query
  });
}

// 查询角色未授权用户列表
export function unallocatedUserList(query) {
  return request({
    url: '/system/role/authUser/unallocatedList',
    method: 'get',
    params: query
  });
}

// 取消用户授权角色
export function authUserCancel(data) {
  return request({
    url: '/system/role/authUser/cancel',
    method: 'put',
    data
  });
}

// 批量取消用户授权角色
export function authUserCancelAll(data) {
  return request({
    url: '/system/role/authUser/cancelAll',
    method: 'put',
    params: data
  });
}

// 授权用户选择
export function authUserSelectAll(data) {
  return request({
    url: '/system/role/authUser/selectAll',
    method: 'put',
    params: data
  });
}

// 根据角色ID查询部门树结构
export function deptTreeSelect(roleId) {
  return request({
    url: '/system/role/deptTree/' + roleId,
    method: 'get'
  });
}
