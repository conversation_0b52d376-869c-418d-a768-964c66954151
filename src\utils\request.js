/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-07-31 13:50:23
 */
import axios from 'axios';
import { ElMessage, ElLoading } from 'element-plus';
// ElMessageBox
import { getToken } from '@/utils/auth';
import errorCode from '@/utils/errorCode';
import { tansParams, blobValidate } from '@/utils/index';
import cache from '@/plugins/cache';
import { saveAs } from 'file-saver';
import useUserStore from '@/store/modules/user';
import i18n from '@/locales/index';
const { t } = i18n.global;
let downloadLoadingInstance;
// 是否显示重新登录
export const isRelogin = { show: false };

axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  // baseURL: import.meta.env.VITE_APP_BASE_API,
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // 超时
  timeout: 0
});

// request拦截器
service.interceptors.request.use(
  config => {
    // 是否需要设置 token
    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    if (getToken()) {
      config.headers['Authorization'] = getToken(); // 让每个请求携带自定义token 请根据实际情况自行修改
    }
    // get请求映射params参数
    if (config.method === 'get' && config.params) {
      let url = config.url + '?' + tansParams(config.params);
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {
      const requestObj = {
        url: config.url,
        data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,
        time: new Date().getTime()
      };
      const sessionObj = cache.session.getJSON('sessionObj');
      if (sessionObj === undefined || sessionObj === null || sessionObj === '') {
        cache.session.setJSON('sessionObj', requestObj);
      }
    }
    return config;
  },
  error => {
    Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  res => {
    // 未设置状态码则默认成功状态
    // const code = res.data.code || 200;
    // 获取错误信息
    // const msg = errorCode[code] || res.data.msg || errorCode['default'];
    const msg = res.data.errMessage || errorCode['default'];
    // 二进制数据则直接返回
    if (res.request.responseType === 'blob' || res.request.responseType === 'arraybuffer') {
      return res.data;
    }
    // 如果接口请求正常 直接返回数据  否则提示异常
    if (res.data.success) {
      return Promise.resolve(res.data);
    } else {
      ElMessage({ message: msg || t('request.timeout'), type: 'error' });
      if (res.data.errCode == 401) {
        isRelogin.show = false;
        useUserStore()
          .logOut()
          .then(() => {
            location.href = '';
          });
      }
      return Promise.reject(new Error(msg));
    }
  },
  error => {
    let { message, response } = error;
    if (message == 'Network Error') {
      message = t('request.networkerror');
    }
    if (message.includes('timeout')) {
      message = t('request.timeout');
    }
    if (message.includes('Request failed with status code')) {
      message = t('request.networkerror2') + message.substr(message.length - 3) + t('request.networkerror3');
    }
    if (response.status == 401 || response.data.errCode == 401) {
      if (!isRelogin.show) {
        ElMessage({ message: response.data.errMessage, type: 'error', duration: 5 * 1000 });
        isRelogin.show = true;
        useUserStore()
          .logOut()
          .then(() => {
            location.href = '';
          });
      }
      return Promise.reject(new Error(t('request.relogin')));
    }
    const errMessage = response.data.errMessage || '';
    ElMessage({ message: errMessage || message, type: 'error', duration: 5 * 1000 });
    return Promise.reject(error);
  }
);

// 通用下载方法
export function download(url, params, filename, config) {
  downloadLoadingInstance = ElLoading.service({ text: '正在下载数据，请稍候', background: 'rgba(0, 0, 0, 0.7)' });
  return service
    .post(url, params, {
      transformRequest: [
        params => {
          return tansParams(params);
        }
      ],
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
      responseType: 'blob',
      ...config
    })
    .then(async data => {
      const isBlob = blobValidate(data);
      if (isBlob) {
        const blob = new Blob([data]);
        saveAs(blob, filename);
      } else {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default'];
        ElMessage.error(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch(r => {
      console.error(r);
      ElMessage.error('下载文件出现错误，请联系管理员！');
      downloadLoadingInstance.close();
    });
}

export default service;
