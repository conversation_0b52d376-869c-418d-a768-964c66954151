<template>
  <div id="loginId" class="login">
    <!-- :style="`background-image:url(${dymicSource.bgImg})`" -->
    <div class="login-main">
      <div class="login-main-item">
        <!-- <div class="w-title">Welcome</div> -->
        <div class="w-img">
          <img :src="dymicSource.leftbgImg" alt="" />
        </div>
      </div>
      <div class="login-main-item">
        <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
          <div class="login-form-welcome">{{ $t('login.welcomeinfo') }}</div>
          <h3 class="login-form-title">{{ dymicSource.rightTitle }}</h3>
          <el-form-item prop="username" style="margin-bottom: 30px !important">
            <el-input
              v-model="loginForm.username"
              type="text"
              size="large"
              auto-complete="off"
              :placeholder="$t('login.username')"
            >
              <template #prefix><svg-icon icon-class="user" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              size="large"
              auto-complete="off"
              :placeholder="$t('login.password')"
              @keyup.enter="handleLogin"
            >
              <template #prefix><svg-icon icon-class="password" class="el-input__icon input-icon" /></template>
            </el-input>
          </el-form-item>
          <el-checkbox v-model="loginForm.rememberMe" style="margin: 0 0 25px">{{ $t('login.rememberMe') }}</el-checkbox>
          <el-form-item style="width: 100%; margin-top: 34px">
            <el-button
              :loading="loading"
              size="large"
              type="primary"
              style="width: 100%; height: 50px; font-size: 20px; border-radius: 10px"
              @click.prevent="handleLogin"
            >
              <span v-if="!loading">{{ $t('login.loginbtn') }}</span>
              <span v-else>{{ $t('login.loginbtn2') }}</span>
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!--  左上logo  -->
    <div class="login-logo">
      <img :src="dymicSource.rightLogo" alt="" />
    </div>
    <!--  底部  -->
    <div class="login-footer">
      <span>copyright @ {{ currentYear }} 苏州普中智能科技有限公司</span>
    </div>
  </div>
</template>

<script setup>
import Cookies from 'js-cookie';
import { encrypt, decrypt } from '@/utils/jsencrypt';
import useUserStore from '@/store/modules/user';
import { getCurrentInstance, ref } from 'vue';
import { useRouter } from 'vue-router';
import { requireImg } from '@/utils/file';
import { getAppearanceInfo2 } from '@/api/system/user';
const userStore = useUserStore();
const router = useRouter();
const { proxy } = getCurrentInstance();
const loginForm = ref({
  username: '',
  password: '',
  rememberMe: false,
  uuid: ''
});

const currentYear = ref(window.$dayjs(new Date()).format('YYYY'));

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: window.$t('login.usernameplaceholder') }],
  password: [{ required: true, trigger: 'blur', message: window.$t('login.passwordplaceholder') }]
};
const dymicSource = ref({
  bgImg: '',
  leftbgImg: requireImg('login-leftbg.png'),
  rightLogo: '',
  rightTitle: ''
});
function getImgs() {
  getAppearanceInfo2().then(res => {
    const data = res.data[0];
    dymicSource.value.bgImg = data.loginBackgroundUrl
      ? import.meta.env.VITE_APP_BASE_API + data.loginBackgroundUrl
      : requireImg('login-background.png');
    document.getElementById('loginId').style.backgroundImage = `url('${dymicSource.value.bgImg}')`;
    dymicSource.value.rightTitle = data.loginLogName || '普中开发框架V2.0';
    dymicSource.value.rightLogo = data.loginLogUrl
      ? import.meta.env.VITE_APP_BASE_API + data.loginLogUrl
      : requireImg('login-logo.png');
  });
}
getImgs();

const loading = ref(false);
const redirect = ref(undefined);
function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set('username', loginForm.value.username, { expires: 30 });
        Cookies.set('password', encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set('rememberMe', loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove('username');
        Cookies.remove('password');
        Cookies.remove('rememberMe');
      }
      localStorage.setItem('loginTime', window.$dayjs(new Date()).format('YYYY-MM-DD HH:MM:ss'));
      // 调用action的登录方法
      userStore
        .login(loginForm.value)
        .then(() => {
          router.push({ path: redirect.value || '/terminalrun' });
        })
        .catch(() => {
          loading.value = false;
        });
    }
  });
}
function getCookie() {
  const username = Cookies.get('username');
  const password = Cookies.get('password');
  const rememberMe = Cookies.get('rememberMe');
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

function clearTag() {
  proxy.$tab.closeAllPage().then(({ visitedViews }) => {});
}
clearTag();

getCookie();
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-size: cover;

  &-main {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 588px;

    &-item {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 588px;
      height: 100%;

      // .w-title {
      //   margin-top: 80px;
      //   margin-bottom: 70px;
      //   font-size: 50px;
      //   font-weight: 700;
      //   color: #ffffff;
      //   text-align: center;
      // }
      .w-img {
        text-align: center;

        img {
          width: 426px;
          height: 458px;
        }
      }

      .login-form {
        width: 360px;

        // padding: 25px 25px 5px;
        margin: 60px auto 0;
        background: #ffffff;
        border-radius: 6px;

        &-welcome {
          font-size: 20px;
          font-weight: 400;
          color: #333333;
        }

        &-title {
          margin-top: 10px;
          font-size: 32px;
          font-weight: 500;
          line-height: 37px;
          color: #333333;

          // color: var(--el-color-primary);
          text-align: left;
        }

        .input-icon {
          width: 14px;
          height: 39px;
          margin-left: 0;
        }
      }
    }

    .login-main-item:nth-child(1) {
      background-color: #edf6fb;
      border-top-left-radius: 24px;
      border-bottom-left-radius: 24px;
    }

    .login-main-item:nth-child(2) {
      background-color: rgb(255 255 255 / 100%);
      border-top-right-radius: 24px;
      border-bottom-right-radius: 24px;
    }
  }

  &-footer {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 50px;
    font-family: Arial;
    font-size: 12px;
    line-height: 50px;
    color: #ffffff;
    text-align: center;
    letter-spacing: 1px;

    // background: #9ec5fb;
  }

  &-logo {
    position: fixed;
    top: 63px;
    left: 96px;

    img {
      max-width: 204px;
    }
  }
}

.login-tip {
  font-size: 13px;
  color: #bfbfbf;
  text-align: center;
}

.login-code {
  float: right;
  width: 33%;
  height: 40px;

  img {
    vertical-align: middle;
    cursor: pointer;
  }
}

.login-code-img {
  height: 40px;
  padding-left: 12px;
}
</style>
