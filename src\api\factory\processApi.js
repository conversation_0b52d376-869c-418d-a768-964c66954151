import request from '@/utils/request';

// 获取工序
export const getPageApi = (data, params = {}) =>
  request({
    url: `/craftProcessInfo/s`,
    method: 'post',
    data,
    params
  });

// 新增工序
export const addPageApi = data =>
  request({
    url: `/craftProcessInfo`,
    method: 'post',
    data
  });

// 修改工序
export const editPageApi = data =>
  request({
    url: `/craftProcessInfo/${data.id}`,
    method: 'put',
    data
  });

// 删除工序
export const delPageApi = data =>
  request({
    url: `/craftProcessInfo/${data.id}`,
    method: 'delete',
    params: {
      batch: true
    }
  });