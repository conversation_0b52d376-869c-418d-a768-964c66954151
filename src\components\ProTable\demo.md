<!--
 * @Author: babyyage
 * @Date: 2023-06-07 09:43:43
 * @LastEditTime: 2023-06-07 13:32:46
-->

# 超级表格

## 简述：搜索框+表格+分页器

## 用法一：搜索框部分

### 1.js column 数据与表格数据公用 数组中如果有 search 则会在搜索框中显示

    `const columns = [{ type: 'selection', fixed: 'left', width: 80 },{ type: 'index', label: '#', width: 80 },{ type: 'expand', label: 'Expand', width: 100 },{
    prop: 'username',
    label: '用户姓名',
    search: { el: 'input' },
    render: scope => {
      return (
        <el-button type="primary" link onClick={() => ElMessage.success('我是通过 jsx 语法渲染的内容')}>
          {scope.row.username}
        </el-button>
      );
    }}, {
    prop: 'gender',
    label: '性别',
    // 字典数据
    // enum: genderType,
    // 字典请求不带参数
    enum: getUserGender,
    // 字典请求携带参数
    // enum: () => getUserGender({ id: 1 }),
    search: { el: 'select', props: { filterable: true } },
    fieldNames: { label: 'genderLabel', value: 'genderValue' }},// 多级 prop{ prop: 'user.detail.age', label: '年龄', search: { el: 'input' } },{ prop: 'idCard', label: '身份证号', search: { el: 'input' } },{ prop: 'email', label: '邮箱' },{ prop: 'address', label: '居住地址' }, {
    prop: 'status',
    label: '用户状态',
    enum: getUserStatus,
    search: { el: 'tree-select', props: { filterable: true } },
    fieldNames: { label: 'userLabel', value: 'userStatus' },
    render: scope => {
      return (
        <>
          {BUTTONS.value.status ? (
            <el-switch
              model-value={scope.row.status}
              active-text={scope.row.status ? '启用' : '禁用'}
              active-value={1}
              inactive-value={0}
              onClick={() => changeStatus(scope.row)}
            />
          ) : (
            <el-tag type={scope.row.status ? 'success' : 'danger'}>{scope.row.status ? '启用' : '禁用'}</el-tag>
          )}
        </>
      );
    }}];`

| search 对象中的 key | 说明                |          用法           |
| ------------------- | ------------------- | :---------------------: |
| el                  | 实际对应的组件名称  |     如 date-picker      |
| span                | 栅格布局中所占 span |                         |
| props               | 支持组件自身属性    | 如 type size filterable |

特殊说明：如果组件带有选项 如下拉 单选 多选 则在 search 同级配置 **enum** 支持静态数据和接口数据 enum 默认 lable(显示值) value(绑定值), 如对 key value 名称特殊处理 可通过设置 fieldNames `fieldNames: { label: 'genderLabel', value: 'genderValue' }}`

## 用法二：表格部分

### 1.html

`<ProTable
      ref="proTable"
      :columns="columns"
      :request-api="getTableList"
      :init-param="initParam"
      :data-callback="dataCallback"
    >
    </ProTable>`
| table 中的 key | 说明 | 用法/类型 |
| -------- | ----- | :----: |
| columns | 表格头部信息 | 参考搜索框 colums |
| request-api | 表格接口数据回调 | function |
| requestAuto | 表格接口是否自动触发，默认 true ,如果想手动触发可以通过 proTable.value.getTableList(); | bool |
| init-param | 如果表格需要初始化请求参数，直接定义传给 ProTable(之后每次请求都会自动带上该参数，此参数更改之后也会一直带上，改变此参数会自动刷新表格数据) | object |
| dataCallback | 是对于返回的表格数据做处理，如果你后台返回的数据不是 list && total && pageNum && pageSize 这些字段，那么你可以在这里进行处理成这些字段 | function |
| handleData | 如果想对接口数据特殊处理 先设置 true 再传 handleDataFunc 处理数据的回调 | bool |
| handleDataFunc | 对接口数据特殊处理的回调 | function |
| pagination | 是否显示分页器 默认 true | bool |
| border | 是否显示表格边框 默认 true | bool |
| isShowSearch | 是否显示搜索框部分 默认 true | bool |
| rowKey | 表格数据唯一 key 默认 id | string |
| 可以设置 el-table 组件自身属性 | | |

针对表格 column 做个说明
| column 中的 key | 说明 | 用法/类型 |
| -------- | ----- | :----: |
| label、props、width | 可以设置 el-table-column 组件自身属性包括 fixed 等 | |
| 操作列先设置 prop 然后在<ProTable></ProTable>里设置对应的 slot,也可直接使用 render | | 方式一： `<template#operation="scope"><el-button type="primary" link icon="View" @click="openDrawer('查看', scope.row)">查看</el-button></template#operation=>` 方式二：` render: scope => {return ( <><el-tag type={scope.row.status ? 'success' : 'danger'}>{scope.row.status ? '启用' : '禁用'}</el-tag> </>);}`|
