/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-28 16:33:51
 */
// cover some element-ui styles
.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0;
  }
}

.small-padding {
  .cell {
    padding-right: 5px;
    padding-left: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    width: 60px;
    padding: 7px 10px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  position: relative;
  left: 0;
  margin: 0 auto;
  transform: none;
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

/* proTable */
.table-box {
  // padding: 10px;
  // margin: 10px;
  // border: 1px solid var(--el-border-color-light);
}

.el-menu-item.is-active {
  background: #ffffff;
  border-radius: 9px;
}

.table-box,
.table-main {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: 100%;

  // table-search 表格搜索样式
  .table-search {
    // padding: 18px 18px 0;
    // margin-bottom: 10px;
    .el-form {
      .el-form-item__content > * {
        width: 100%;
      }

      // 去除时间选择器上下 padding
      .el-range-editor.el-input__wrapper {
        padding: 0 10px;
      }
    }

    .operation {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 18px;
    }
  }

  // 表格 header 样式
  .table-header {
    .header-button-lf {
      float: left;
    }

    .header-button-ri {
      float: right;
    }

    .el-button {
      margin-bottom: 15px;
    }
  }

  // el-table 表格样式
  /* stylelint-disable-next-line selector-pseudo-element-no-unknown */
  :deep .el-table {
    flex: 1;

    // 修复 safari 浏览器表格错位 https://github.com/HalseySpicy/Geeker-Admin/issues/83
    table {
      width: 100%;
    }

    .el-table__header th {
      height: 45px;
      font-size: 15px;
      font-weight: bold;
      color: var(--el-text-color-primary);
      background: var(--el-fill-color-light);
    }

    .el-table__row {
      height: 45px;
      font-size: 14px;

      .el-table__placeholder {
        display: inline;
      }
    }

    // 设置 el-table 中 header 文字不换行，并省略
    .el-table__header .el-table__cell > .cell {
      white-space: nowrap;
    }

    // 解决表格数据为空时样式不居中问题(仅在element-plus中)
    /* stylelint-disable-next-line selector-pseudo-element-no-unknown */
    .el-table__empty-block {
      position: absolute;
      top: 50%;
      left: 50%;
      padding: 10px 0 !important;
      transform: translate(-50%, -50%);

      /* stylelint-disable-next-line selector-pseudo-element-no-unknown */
      span.el-table__empty-text {
        line-height: 24px;
      }

      /* stylelint-disable-next-line selector-pseudo-element-no-unknown */
      :v-deep .table-empty {
        padding: 10px 0;
        line-height: 30px !important;
      }
    }

    // table 中 image 图片样式
    .table-image {
      width: 50px;
      height: 50px;
      border-radius: 50%;
    }
  }

  // 表格 pagination 样式
  .el-pagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}

/* custom card */
.card {
  box-sizing: border-box;
  padding: 20px;
  overflow-x: hidden;
  background-color: var(--el-bg-color);

  // border-radius: 6px;
  box-shadow: 0 0 12px rgb(0 0 0 / 5%);
}

/* ProTable 不需要 card 样式（在组件内使用 ProTable 会使用到） */
.no-card {
  .card {
    padding: 0;
    background-color: transparent;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .table-search {
    padding: 18px 0 0 !important;
    margin-bottom: 0 !important;
  }
}

.el-dialog__footer {
  border-top: 1px solid #dcdfe6;
}

.el-dialog__header {
  margin-right: 0 !important;
  border-bottom: 1px solid #dcdfe6;
}

.el-form-item {
  margin-bottom: 25px !important;
}

.el-popover.el-popper {
  padding: 28px 32px 32px !important;
}

.el-drawer__header {
  padding-bottom: 10px !important;
  border-bottom: 1px solid #dcdfe6;
}
