import { enusJSONExt } from 'com-businessnew';
const enobj = enusJSONExt || {};
export const enusJSON = {
  ...enobj,
  general: {
    btnyes: 'yes',
    btnno: 'no',
    btndetail: 'detail',
    btnadd: 'add',
    btndelete: 'delete',
    btnremove: 'remove',
    btnedit: 'edit',
    btnmore: 'more',
    btnconfirm: 'confirm',
    btnoperate: 'operate',
    btncancel: 'cancel',
    btnenable: 'enabled',
    btndisabled: 'disabled',
    btnmoveup: 'moveup',
    btnmovedown: 'movedown',
    btnback: 'back',
    btnsave: 'save',
    btnimport: 'import',
    btnexport: 'export',
    placeholderchoose: 'please choose',
    placeholderinput: 'please enter',
    warningInfo: 'warning',
    warningIsNot: 'is',
    warningSuccess: 'success',
    warningFail: 'fail',
    warningImportSuccess: 'import successfully',
    warningExportSuccess: 'export successfully',
    warningExportError: 'export unsuccessfully',
    warningSaveSuccess: 'save successfully',
    warningAddSuccess: 'add successfully',
    warningEditSuccess: 'edit successfully',
    tablesort: 'number',
    tableremark: 'remark',
    tableremarkplaceholder: 'please enter remark',
    tablecreateTime: 'createTime',
    tablestatus: 'status',
    tablestatusplaceholder: 'please choose status',
    tableall: 'all',
    tableupdateUserName: 'updateUserName',
    tableupdateTime: 'updateTime'
  },
  menu: {
    ...enobj.menuTwo,
    home: 'home',
    system: 'system',
    sysnotice: 'sysnotice',
    personalspace: 'personalspace',
    workspace: 'workspace',
    sysboard: 'sysboard',
    user: 'user',
    post: 'post',
    role: 'role',
    dept: 'dept',
    staffList: 'staff',
    loginLogs: 'loginLogs',
    operateLogs: 'operateLogs',
    appearanceSet: 'appearanceSet',
    labelList: 'label',
    notice: 'notice',
    menu: 'menu',
    menuGroupList: 'menuGroup',
    receiver: 'receiver',
    channel: 'channel',
    messageTemp: 'messageTemp',
    noticeRecord: 'noticeRecord'
  },
  login: {
    welcomeinfo: 'Hello, welcome to login',
    username: 'username',
    password: 'password',
    usernameplaceholder: 'please enter username',
    passwordplaceholder: 'please enter password',
    rememberMe: 'remeber password',
    loginbtn: 'login',
    loginbtn2: 'logining...',
    editpassword: 'modify password',
    editpasswordsuccess: 'Modify successfully, please log in again',
    logout: 'log out',
    logout2: 'Are you sure to log out and exit the system?',
    oldpsd: 'old password',
    newpsd: 'new password',
    newpsd2: 're-enter password',
    oldpsdplaceholder: 'please enter old password',
    newpsdplaceholder: 'please enter new password',
    newpsd2placeholder: 'please enter re-enter password',
    newandnewnot: 'The new password is inconsistent. please re-enter it'
  },
  request: {
    timeout: 'The system interface request timed out',
    networkerror: 'The back-end interface is improperly connected. ',
    networkerror2: 'system interface',
    networkerror3: 'Anomaly ',
    relogin: 'Invalid session, or the session has expired, please log in again.'
  },
  components: {
    uploadwarn1: 'please upload',
    uploadsizewarn: "The total size of the input file doesn't exceed",
    uploadformatwarn: 'The format is',
    uploadformatwarn2: 'file',
    uploadformatwarn3: 'The file format is incorrect, only supported',
    uploadformatwarn4: 'The size of the uploaded profile picture cannot exceed',
    uploadformatwarn5: 'please wait while uploading pictures...',
    uploadformatwarn6: 'The number of uploaded files cannot exceed',
    uploadformatwarn7: 'fail to upload',
    uploadchoosefile: 'select file',
    review: 'preview'
  },
  home: {
    personalSpace: {
      username: 'username',
      userage: 'age',
      userjobnum: 'job number',
      userdept: 'department',
      usersex: 'gender',
      userpost: 'post',
      usercount: 'account',
      userstaff: 'staff',
      titleworkspace: 'workspace',
      titlesysboard: 'system board',
      titlenotice: 'announcement',
      titlenoticeList: 'list of announcements',
      titlenoticetitle: 'announcement title',
      titlenoticecontent: 'announcement content',
      titlenoticepublishuser: 'released account',
      titlecmodel: 'enterprise model',
      titlememo: 'memo',
      titlememoadd: 'add memo',
      titlememonodata: 'no memo',
      noticeWelcome: 'Hello, welcome to login and use, I wish you a smooth work~',
      noticetime: 'log-in time',
      publishtime: 'released time',
      publishuser: 'publisher',
      premonth: 'last month',
      today: 'today',
      nextmonth: 'next month',
      monday: 'Monday',
      tuesday: 'Tuesday',
      wednesday: 'Wednesday',
      thursday: 'Thursday',
      friday: 'Friday',
      saturday: 'Saturday',
      sunday: 'Sunday'
    },
    workSpace: {
      searchplaceholder: 'please enter a menu name',
      menuadd: 'add menu',
      menuaddwarning: 'please choose first!',
      menuname: 'menu name',
      menumodule: 'owning module'
    },
    systemBoard: {
      searchplaceholder: 'please enter the board name/board link/remarks',
      boardadd: 'add board',
      boardedit: 'edit board',
      boardicon: 'board icon',
      boardname: 'board name',
      boardurl: 'board url',
      boardiconplaceholder: 'please choose board icon',
      boardnameplaceholder: 'please enter board name',
      boardurlplaceholder: 'please enter board url',
      boardbtnreview: 'preview'
    },
    notice: {
      publishnotice: 'announce',
      title: 'announcement title',
      content: 'announcement content',
      titleplaceholder: 'please enter the announcement title',
      contentplaceholder: 'please enter the bulletin content',
      reset: 'reset',
      publish: 'release',
      publishrecord: 'announcement record'
    }
  },
  system: {
    userInfo: {
      searchplaceholder: 'please enter the account name/account number',
      userplace: 'user',
      userplaceTwo: 'please select User',
      userInfoadd: 'add account',
      userInfoedit: 'edit account',
      name: 'account name',
      account: 'account',
      role: 'role',
      password: 'password',
      nameplaceholder: 'please enter the account name',
      accountplaceholder: 'please enter your account number',
      roleplaceholder: 'please select role',
      passwordplaceholder: 'please enter password',
      resetPsd: 'reset password',
      resetPsdSuccess: 'reset successfully',
      resetPsd2: 'reset password',
      resetPsd2placeholder: 'please enter the reset password'
    },
    post: {
      searchplaceholder: 'please enter name/number/remarks',
      postadd: 'add post',
      postedit: 'edit post',
      memberadd: 'add users',
      memberremove: 'remove users',
      name: 'post name',
      postCode: 'post code',
      membersCount: 'post members',
      nameplaceholder: 'please enter the post name',
      postCodeplaceholder: 'please enter the post code',
      membersCounttodetail: 'view post staff'
    },
    staff: {
      searchplaceholder: 'please enter your name/job number/phone number/department/post',
      staffadd: 'add staff',
      staffedit: 'edit staff',
      name: 'name',
      jobCode: 'job code',
      gender: 'gender',
      genderman: 'male',
      genderwomen: 'female',
      age: 'age',
      phone: 'phone number',
      deptName: 'dept name',
      postName: 'post name',
      nameplaceholder: 'please enter name',
      ageplaceholder: 'please enter age',
      jobCodeplaceholder: 'please enter job code',
      phoneplaceholder: 'please enter phone number',
      deptNameplaceholder: 'please choose dept name',
      postNameplaceholder: 'please choose post name',
      linkeduserplaceholder: 'please choose linked user account',
      linkeduser: 'associated account'
    },
    role: {
      searchplaceholder: 'please enter name',
      roleadd: 'add role',
      roleedit: 'edit role',
      name: 'role name',
      orderNo: 'role number',
      funcaccess: 'functional authority',
      funcaccessrefresh: 'update authority',
      accountcontrol: 'account management',
      accountadd: 'add account',
      accountdelete: 'remove account'
    },
    dept: {
      searchplaceholder: 'please enter name/number/remarks',
      name: 'industry title',
      deptCode: 'department number',
      nameplaceholder: 'please enter a department name',
      deptCodeplaceholder: 'please enter the department number',
      deptMember: 'department staff',
      memberCount: 'number of people'
    },
    loginLogs: {
      searchplaceholder: 'please enter the account name /IP address',
      loginterminal: 'login terminal',
      loginDate: 'date of entry',
      ip: 'IP address'
    },
    operateLogs: {
      searchplaceholder: 'please enter name/number',
      operateDate: 'date of operation',
      operateRemark: 'operation description',
      inputParams: 'in parameter',
      outParams: 'out parameter'
    },
    appearanceSet: {
      loginlogo: 'web - login interfaceLOGO',
      loginsystemName: 'web - login page System name',
      loginbg: 'web - background of the login page',
      hometitle: 'web - the upper left corner of the system is displayed',
      homebg: 'web - dystem background',
      showimg: 'display picture',
      showname: 'display name',
      uploadimgplace: 'please upload pictures'
    },
    menu: {
      menuadd: 'add menu',
      menuedit: 'edit menu',
      searchplaceholder: 'please enter a menu name',
      name: 'menu name',
      groupTypeName: 'group type',
      icon: 'menu icon',
      orderNo: 'orderNo',
      perms: 'permission identification',
      component: 'component path',
      path: 'routing address',
      pathparams: 'route parameter',
      parentMenu: 'previous menu',
      iscache: 'cache or not',
      isshow: 'display status',
      isouturl: 'outside chain or not',
      menuType: 'menu type',
      isenable: 'menu status',
      apiUrl: 'interface address',
      nameplaceholder: 'please enter a menu name',
      groupTypeNameplaceholder: 'please select a menu group',
      iconplaceholder: 'please select icon',
      orderNoplaceholder: 'please enter serial number',
      permsplaceholder: 'please enter a permission identifier',
      componentplaceholder: 'please enter the component path',
      pathplaceholder: 'please enter the routing address',
      pathparamsplaceholder: 'please enter route parameters',
      parentMenuplaceholder: 'please select the upper menu',
      menuTypeplaceholder: 'please select a menu type',
      apiUrlplaceholder: 'please enter the interface address',
      pathtooltip1: "if external link is selected, the routing address needs to start with 'http(s)://'",
      pathtooltip2:
        "the routing address of the access, such as: 'user', if the external network address requires internal link access, start with 'http(s)://'",
      option_CATALOG: 'catalog',
      option_MENU: 'menu',
      option_BUTTON: 'button',
      option_CACHE: 'cache',
      option_NOTCACHE: 'not cache',
      option_SHOW: 'show',
      option_HIDDEN: 'hidden',
      toggle: 'unfold/fold'
    },
    menuGroup: {
      searchplaceholder: 'please enter name/number',
      menuGroupadd: 'add menu group',
      menuGroupedit: 'Edit menu group',
      name: 'name',
      code: 'code',
      nameplaceholder: 'please enter name',
      codeplaceholder: 'please enter the number'
    }
  },
  sysnotice: {
    receiver: {
      searchplaceholder: 'please enter the recipient name/account number',
      receiveradd: 'add recipient',
      receiveredit: 'edit recipient',
      receiverremove: 'remove recipient',
      receiverimport: 'import recipient',
      datasource: 'datasource',
      name: 'recipient',
      nameplaceholder: 'please enter the recipient name',
      accountorname: 'account name/account number',
      channel: 'notification channel',
      channelsearchplaceholder: 'please enter the notification channel/receiving ID',
      channelconfig: 'allocation channel',
      channelbind: 'binding channel',
      channelbindsuccess: 'binding successful',
      channelremove: 'channel removal',
      receivetype: 'receiving identifier',
      open: 'open',
      close: 'close'
    },
    channel: {
      dingding: 'dingding',
      wechat: 'wechat',
      email: 'email',
      sysnotic: 'sysnotice',
      corpId: 'corpId',
      corpSecret: 'corpSecret',
      agentId: 'agentId',
      mailName: 'e-mail address',
      mailPassword: 'Email authorization code',
      smtp: 'SMTP host name',
      smtpPort: 'Host port number'
    },
    messageTemp: {
      searchplaceholder: 'Enter the template name/template number/service type/template content',
      messageTempadd: 'add template',
      messageTempedit: 'edit template',
      name: 'template name',
      code: 'template number',
      type: 'business type',
      format: 'template content',
      nameplaceholder: 'please enter a template name',
      codeplaceholder: 'please enter the template number',
      typeplaceholder: 'please enter the business type',
      formatplaceholder: 'please enter the template content',
      formatplaceholder2: '{$name} test message template sending {$company}'
    },
    noticeRecord: {
      searchplaceholder: 'please enter the recipient/business type/notification channel/notification content',
      createTime: 'notification time',
      content: 'notice content',
      resetnotice: 'resend message',
      resetnoticebtn: 'retry'
    }
  }
};
