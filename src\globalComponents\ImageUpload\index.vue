<template>
  <div class="component-upload-image">
    <el-upload
      ref="imageUpload"
      multiple
      :action="uploadImgUrl"
      list-type="picture-card"
      :on-success="handleUploadSuccess"
      :before-upload="handleBeforeUpload"
      :limit="limit"
      :on-error="handleUploadError"
      :on-exceed="handleExceed"
      :before-remove="handleDelete"
      :show-file-list="true"
      :headers="headers"
      :file-list="fileList"
      :on-preview="handlePictureCardPreview"
      :class="{ hide: fileList.length >= limit }"
    >
      <el-icon class="avatar-uploader-icon"><plus /></el-icon>
    </el-upload>
    <!-- 上传提示 -->
    <div v-if="showTip" class="el-upload__tip">
      {{ $t('components.uploadwarn1') }}
      <template v-if="fileSize">
        {{ $t('components.uploadsizewarn') }} <b style="color: #f56c6c">{{ fileSize }}MB</b>
      </template>
      <template v-if="fileType">
        {{ $t('components.uploadformatwarn') }} <b style="color: #f56c6c">{{ fileType.join('/') }}</b>
      </template>
      {{ $t('components.uploadformatwarn2') }}
    </div>

    <el-dialog v-model="dialogVisible" :title="$t('components.review')" width="800px" append-to-body>
      <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
    </el-dialog>
  </div>
</template>

<script setup name="ImageUpload">
import { getToken } from '@/utils/auth';
const props = defineProps({
  modelValue: {
    type: [String, Object, Array],
    default: () => []
  },
  // 图片数量限制
  limit: {
    type: Number,
    default: 5
  },
  // 大小限制(MB)
  fileSize: {
    type: Number,
    default: 200
  },
  // 文件类型, 例如['png', 'jpg', 'jpeg']
  fileType: {
    type: Array,
    default: () => ['png', 'jpg', 'jpeg']
  },
  // 是否显示提示
  isShowTip: {
    type: Boolean,
    default: true
  }
});

const { proxy } = getCurrentInstance();
const emit = defineEmits(['update:modelValue']);
const number = ref(0);
const uploadList = ref([]);
const dialogImageUrl = ref('');
const dialogVisible = ref(false);
const baseUrl = ref(import.meta.env.VITE_APP_BASE_API);
const uploadImgUrl = ref(baseUrl.value + '/userInfo/upload'); // 上传的图片服务器地址
const headers = ref({ Authorization: getToken() });
const fileList = ref([]);
const showTip = computed(() => props.isShowTip && (props.fileType || props.fileSize));

watch(
  () => props.modelValue,
  val => {
    if (val) {
      // 首先将值转为数组
      // const list = Array.isArray(val) ? val : props.modelValue.split(',');
      fileList.value = val.map(ele => {
        return {
          ...ele,
          url: import.meta.env.VITE_APP_BASE_API + ele.url
        };
      });
    } else {
      fileList.value = [];
      return [];
    }
  },
  { deep: true, immediate: true }
);

// 上传前loading加载
function handleBeforeUpload(file) {
  let isImg = false;
  if (props.fileType.length) {
    let fileExtension = '';
    if (file.name.lastIndexOf('.') > -1) {
      fileExtension = file.name.slice(file.name.lastIndexOf('.') + 1);
    }
    fileExtension = fileExtension.toLowerCase();
    isImg = props.fileType.some(type => {
      if (file.type.indexOf(type) > -1) return true;
      if (fileExtension && fileExtension.indexOf(type) > -1) return true;
      return false;
    });
  } else {
    isImg = file.type.indexOf('image') > -1;
  }
  if (!isImg) {
    proxy.$modal.msgError(`${window.$t('components.uploadformatwarn3')}${props.fileType.join('/')}`);
    return false;
  }
  if (props.fileSize) {
    const isLt = file.size / 1024 / 1024 < props.fileSize;
    if (!isLt) {
      proxy.$modal.msgError(`${window.$t('components.uploadformatwarn4')} ${props.fileSize} MB!`);
      return false;
    }
  }
  proxy.$modal.loading(window.$t('components.uploadformatwarn5'));
  number.value++;
}

// 文件个数超出
function handleExceed() {
  proxy.$modal.msgError(`${window.$t('components.uploadformatwarn6')} ${props.limit}!`);
}

// 上传成功回调
function handleUploadSuccess(res, file) {
  if (res.success) {
    uploadList.value.push({ name: res.data.filename, url: '/' + res.data.path });
    uploadedSuccessfully();
  } else {
    number.value--;
    proxy.$modal.closeLoading();
    proxy.$modal.msgError(res.msg);
    proxy.$refs.imageUpload.handleRemove(file);
    uploadedSuccessfully();
  }
}

// 删除图片
function handleDelete(file) {
  const findex = fileList.value.map(f => f.name).indexOf(file.name);
  if (findex > -1 && uploadList.value.length === number.value) {
    fileList.value.splice(findex, 1);
    emit('update:modelValue', fileList.value);
    return false;
  }
}

// 上传结束处理
function uploadedSuccessfully() {
  if (number.value > 0 && uploadList.value.length === number.value) {
    fileList.value = fileList.value.filter(f => f.url !== undefined).concat(uploadList.value);
    uploadList.value = [];
    number.value = 0;
    fileList.value = fileList.value.map(ele => {
      let url = ele.url;
      if (url.includes(import.meta.env.VITE_APP_BASE_API)) {
        url = url.split(import.meta.env.VITE_APP_BASE_API)[1];
      }
      return {
        name: ele.name,
        url
      };
    });
    emit('update:modelValue', fileList.value);
    proxy.$modal.closeLoading();
  }
}

// 上传失败
function handleUploadError() {
  proxy.$modal.msgError(window.$t('components.uploadformatwarn7'));
  proxy.$modal.closeLoading();
}

// 预览
function handlePictureCardPreview(file) {
  dialogImageUrl.value = file.url;
  dialogVisible.value = true;
}

// 对象转成指定字符串分隔
// function listToString(list, separator) {
//   let strs = '';
//   separator = separator || ',';
//   for (const i in list) {
//     if (undefined !== list[i].url && list[i].url.indexOf('blob:') !== 0) {
//       strs += list[i].url.replace(baseUrl, '') + separator;
//     }
//   }
//   return strs != '' ? strs.substr(0, strs.length - 1) : '';
// }
</script>

<style scoped lang="scss">
// .el-upload--picture-card 控制加号部分
:deep(.hide .el-upload--picture-card) {
  display: none;
}
</style>
