<!--
 * @Author: babyyage
 * @Date: 2023-05-09 15:50:45
 * @LastEditTime: 2025-05-30 13:24:55
-->
<template>
  <commonModal ref="commonModalRef" dialog-title="生产报工" :footerShow="false">
    <el-form ref="ruleFormRef" label-width="100px" :model="addForm" :rules="rules">
      <el-form-item label="生产单号">
        <el-input v-model="addForm.orderNo" disabled />
      </el-form-item>

      <el-form-item label="产品编号">
        <el-input v-model="addForm.productCode" disabled />
      </el-form-item>

      <el-form-item label="产品名称">
        <el-input v-model="addForm.productName" disabled />
      </el-form-item>

      <el-form-item label="计划数量">
        <el-input v-model="addForm.planNumber" disabled />
      </el-form-item>

      <el-form-item label="完成数量">
        <el-input v-model="addForm.actualNumber" disabled />
      </el-form-item>

      <el-form-item label="报工数" prop="reportNumber">
        <el-input-number v-model="addForm.reportNumber" :step="1" :precision="0" :min="1" style="width:100%;" />
      </el-form-item>
    </el-form>

    <div style="display: flex;justify-content: space-evenly;">
      <el-button size="large" @click="saveData">报工</el-button>
      <el-button type="primary" size="large" @click="endReport">完工</el-button>
    </div>
  </commonModal>
</template>

<script setup>
import * as planApiList from '@/api/product/planApi.js';
import { getCurrentInstance, reactive } from 'vue';
const { proxy } = getCurrentInstance();
const emit = defineEmits(['on-success']);
const commonModalRef = ref();
const ruleFormRef = ref();
const rules = reactive({
  reportNumber: [{ required: true, message: '请输入', trigger: 'blur' }],
});
const addForm = ref({});

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign(
    {},
    val
  );
  addForm.value = obj;
  commonModalRef.value.visible = true;
  proxy.resetForm('ruleFormRef');
}
// 保存数据
function saveData() {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      // commonModalRef.value.addLoading = true;
      let requestObj = null;
      requestObj = planApiList.reportStationOrderApi(addForm.value);
      requestObj
        .then(() => {
          window.$modal.msgSuccess('报工成功');
          commonModalRef.value.visible = false;
          emit('on-success');
        })
        .finally(() => {
          // commonModalRef.value.addLoading = false;
        });
    }
  });
}

const endReport = async () => {
  await window.$useHandleData(planApiList.endPageApi, addForm.value, '完工');
  commonModalRef.value.visible = false;
  emit('on-success');
}

defineExpose({
  openModal
});
</script>
