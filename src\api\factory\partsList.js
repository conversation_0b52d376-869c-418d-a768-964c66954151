/*
 * @Author: babyyage
 * @Date: 2024-09-18 15:33:31
 * @LastEditTime: 2024-10-24 13:38:02
 */
import request from '@/utils/request';

// 查询
export function listpartInfo(data, params) {
  return request({
    url: '/stationPartInfo/s',
    method: 'post',
    data,
    params
  });
}
// 查询
export function selectpartInfo(data) {
  return request({
    url: '/stationPartInfo/s?page=false',
    method: 'post',
    data
  });
}

// 新增工序
export function addpartInfo(data) {
  return request({
    url: '/stationPartInfo',
    method: 'post',
    data
  });
}

// 修改工序
export function updatepartInfo(data) {
  return request({
    url: `/stationPartInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除工序
export function delpartInfo(data) {
  return request({
    url: `/stationPartInfo/${data}`,
    method: 'delete',
    params: { batch: true }
  });
}
