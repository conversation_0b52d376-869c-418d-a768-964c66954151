<!--
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-07-22 17:00:04
-->
<template>
  <!-- <router-view /> -->
  <el-config-provider :locale="messages[locale]">
    <router-view />
  </el-config-provider>
</template>

<script setup>
import useSettingsStore from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import { onMounted, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
const { locale, messages } = useI18n();
onMounted(() => {
  nextTick(() => {
    // 初始化主题样式
    handleThemeStyle(useSettingsStore().theme);
  });
});
</script>

<style lang="scss">
// // 默认图片
.hiprint-printElement-image-content {
  img {
    content: url('./assets/images/label-place.png');
  }
}
</style>
