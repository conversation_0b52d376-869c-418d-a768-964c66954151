/*
 * @Author: babyyage
 * @Date: 2023-04-18 17:31:45
 * @LastEditTime: 2024-03-12 16:00:45
 */
import request from '@/utils/request';

// 查询部门列表
export function listMemo(data) {
  return request({
    url: '/memo/s?page=false',
    method: 'post',
    data
  });
}

// 新增部门
export function addMemo(data) {
  return request({
    url: '/memo',
    method: 'post',
    data
  });
}

// 修改部门
export function updateMemo(data) {
  return request({
    url: `/memo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除部门
export function delMemo(data) {
  return request({
    url: `/memo/${data.id}`,
    method: 'delete',
    params: { batch: true }
  });
}
