import request from '@/utils/request';
// 工单报工记录新建资源
export function add(data) {
  return request({ url: '/orderReportRecordInfo', method: 'post', data });
}
// 工单报工记录查询资源列表
export function list(data) {
  return request({ url: '/orderReportRecordInfo/s', method: 'post', data });
}
// 工单报工记录资源是否存在
export function existing(data) {
  return request({ url: '/orderReportRecordInfo/s/existing', method: 'post', data });
}
// 工单报工记录批量删除资源,ids以，或 -分割
export function dels(data) {
  return request({ url: '/orderReportRecordInfo/' + data, method: 'delete' });
}
// 工单报工记录获取单个资源
export function detail(data) {
  return request({ url: '/orderReportRecordInfo/' + data, method: 'get', params: data });
}
// 工单报工记录修改资源
export function edit(data) {
  return request({ url: '/orderReportRecordInfo/' + data.id, method: 'put', data });
}
// 工单报工记录删除资源
export function del(data) {
  return request({ url: '/orderReportRecordInfo/' + data, method: 'delete' });
}
