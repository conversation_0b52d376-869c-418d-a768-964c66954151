/*
 * @Author: babyyage
 * @Date: 2024-09-18 15:33:31
 * @LastEditTime: 2024-11-01 14:48:17
 */
import request from '@/utils/request';

// 查询
export function listproductInfo(data, params) {
  return request({
    url: '/productInfo/s',
    method: 'post',
    data,
    params
  });
}
// 查询
export function selectproductInfo(data) {
  return request({
    url: '/productInfo/s?page=false',
    method: 'post',
    data
  });
}

// 新增工序
export function addproductInfo(data) {
  return request({
    url: '/productInfo',
    method: 'post',
    data
  });
}

// 修改工序
export function updateproductInfo(data) {
  return request({
    url: `/productInfo/${data.id}`,
    method: 'put',
    data
  });
}

// 删除工序
export function delproductInfo(data) {
  return request({
    url: `/productInfo/${data}`,
    method: 'delete',
    params: { batch: true }
  });
}
