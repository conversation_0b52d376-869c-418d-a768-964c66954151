<!--
 * @Author: babyyage
 * @Date: 2023-05-11 11:32:32
 * @LastEditTime: 2024-07-25 15:54:11
-->
<template>
  <commonModal ref="commonModalRef" :dialog-title="$t('login.editpassword')" @submit-data="saveData">
    <el-form ref="ruleFormRef" label-width="100px" :model="addForm" :rules="rules">
      <el-form-item :label="$t('login.oldpsd')" prop="oldPassword">
        <el-input v-model="addForm.oldPassword" type="password" :placeholder="$t('login.oldpsdplaceholder')" />
      </el-form-item>
      <el-form-item :label="$t('login.newpsd')" prop="newPassword">
        <el-input v-model="addForm.newPassword" type="password" :placeholder="$t('login.newpsdplaceholder')" />
      </el-form-item>
      <el-form-item :label="$t('login.newpsd2')" prop="newPasswordTwo">
        <el-input v-model="addForm.newPasswordTwo" type="password" :placeholder="$t('login.newpsd2placeholder')" />
      </el-form-item>
    </el-form>
  </commonModal>
</template>

<script setup>
import Cookies from 'js-cookie';
import { updatePsd } from '@/api/system/user';
import useUserStore from '@/store/modules/user';
const { proxy } = getCurrentInstance();
const userStore = useUserStore();
const emit = defineEmits(['on-success']);
const commonModalRef = ref();
const ruleFormRef = ref();
const rules = reactive({
  oldPassword: [{ required: true, message: proxy.$t('login.oldpsdplaceholder'), trigger: 'blur' }],
  newPassword: [{ required: true, message: proxy.$t('login.newpsdplaceholder'), trigger: 'blur' }],
  newPasswordTwo: [{ required: true, message: proxy.$t('login.newpsd2placeholder'), trigger: 'blur' }]
});
const addForm = ref({});

// 初始化弹窗
function openModal(val) {
  const obj = Object.assign({ oldPassword: '', newPassword: '', newPasswordTwo: '', id: Cookies.get('userId') }, val);
  addForm.value = obj;
  commonModalRef.value.visible = true;
  proxy.resetForm('ruleFormRef');
}
// 保存数据
function saveData() {
  ruleFormRef.value.validate(valid => {
    if (valid) {
      if (addForm.value.newPassword !== addForm.value.newPasswordTwo) {
        proxy.$modal.msgError(proxy.$t('login.newandnewnot'));
        return;
      }
      commonModalRef.value.addLoading = true;
      let requestObj = null;
      requestObj = updatePsd(addForm.value);
      requestObj
        .then(() => {
          window.$modal.msgSuccess(proxy.$t('login.editpasswordsuccess'));
          commonModalRef.value.visible = false;
          emit('on-success');
          setTimeout(() => {
            userStore.logOut().then(() => {
              location.href = '/index';
            });
          }, 1000);
        })
        .finally(() => {
          commonModalRef.value.addLoading = false;
        });
    }
  });
}
defineExpose({
  openModal
});
</script>
